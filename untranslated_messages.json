{"ja": ["factoryReset", "factoryResetConfirm", "displaySettings", "inputSettings", "outputSettings", "functions", "failed", "eqWavePainterStarted", "targetRawDataPrepared", "sourceFRRawDataPrepared", "combinedRawDataPrepared", "individualBandsRawDataPrepared", "filteredFRRawDataPrepared", "dynamicDbRangeCalculated", "coordinateConverterCreated", "gridAndLabelsDrawn", "drawingTargetCurve", "drawingSourceFRCurve", "drawingIndividualBands", "drawingCombinedCurve", "drawingFilteredFRCurve", "drawingFlatLine", "eqWavePainterFinished", "finalDynamicDbRange"], "ko": ["factoryReset", "factoryResetConfirm", "displaySettings", "inputSettings", "outputSettings", "functions", "failed", "eqWavePainterStarted", "targetRawDataPrepared", "sourceFRRawDataPrepared", "combinedRawDataPrepared", "individualBandsRawDataPrepared", "filteredFRRawDataPrepared", "dynamicDbRangeCalculated", "coordinateConverterCreated", "gridAndLabelsDrawn", "drawingTargetCurve", "drawingSourceFRCurve", "drawingIndividualBands", "drawingCombinedCurve", "drawingFilteredFRCurve", "drawingFlatLine", "eqWavePainterFinished", "finalDynamicDbRange"], "zh": ["failed"], "zh_Hant": ["factoryReset", "factoryResetConfirm", "displaySettings", "inputSettings", "outputSettings", "functions", "failed", "eqWavePainterStarted", "targetRawDataPrepared", "sourceFRRawDataPrepared", "combinedRawDataPrepared", "individualBandsRawDataPrepared", "filteredFRRawDataPrepared", "dynamicDbRangeCalculated", "coordinateConverterCreated", "gridAndLabelsDrawn", "drawingTargetCurve", "drawingSourceFRCurve", "drawingIndividualBands", "drawingCombinedCurve", "drawingFilteredFRCurve", "drawingFlatLine", "eqWavePainterFinished", "finalDynamicDbRange"]}