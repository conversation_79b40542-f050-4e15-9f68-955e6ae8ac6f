{"@@locale": "en", "appName": "Topping Home", "homeTitle": "My Products", "homeAddHint": "Add devices for better experience", "addDevice": "Add <PERSON>", "noDevice": "No Device", "addDeviceHint": "Click to add new device", "foundDevice": "<PERSON>", "devices": "Devices", "loginClick": "Click to Login", "about": "About", "quickStart": "Quick Start", "feedback": "<PERSON><PERSON><PERSON>", "customBackground": "Custom Background", "logout": "Log Out", "registerTitle": "Register", "phone": "Phone Number", "phoneHint": "Please enter your phone number", "verifyCode": "Verification Code", "getSmsVerifyCode": "Get SMS Code", "agreement": "I have read and agree to the", "agreementLink": "User Agreement", "agree": "Agree", "privacyPolicy": "Privacy Policy", "register": "Register", "login": "<PERSON><PERSON>", "loginTitle": "<PERSON><PERSON>", "loginPhoneHint": "Please enter your phone number", "loginVerifyCode": "Verification Code", "loginGetSmsVerifyCode": "Get SMS Code", "loginPassword": "Password", "loginForgetPassword": "Forgot Password", "loginFail": "Login Failed", "passwordError": "Incorrect Password", "phoneNotRegistered": "Phone Number Not Registered", "noAccount": "Don''t have an account?", "registerNow": "Register Now", "tips": "Tips", "graphicCodeHint": "Please enter the graphic code", "agreeToTermsHint": "Please agree to the User Agreement and Privacy Policy", "verificationCodeHint": "Please enter the verification code", "usernameHint": "Please enter your username", "passwordHint": "Please enter your password", "confirmPasswordHint": "Please re-enter your password", "passwordMismatch": "Passwords do not match", "registerSuccess": "Registration Successful", "registerSuccessHint": "Please use your phone number and password to log in", "registerFailed": "Registration Failed", "getVerificationCode": "Get SMS Code", "verificationCodeSent": "Verification code has been sent to", "next": "Next", "secondResend": "s to resend", "settingAccount": "Account <PERSON><PERSON>", "registerComplete": "Registration Complete", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password", "inputError": "Input Error", "inputCannotBeEmpty": "Input cannot be empty", "invalidCaptcha": "<PERSON><PERSON><PERSON>", "forgetPassword": "Forgot Password", "forgetPasswordTitle": "Forgot Password", "forgetPasswordPhoneTitle": "Please enter your phone number", "forgetPasswordPhoneHint": "Use the bound phone number to get a verification code, then proceed", "forgetPasswordVerifyCode": "Verification Code", "forgetPasswordGetSmsVerifyCode": "Get SMS Code", "forgetPasswordNext": "Next Step", "forgetPasswordNewPassword": "New Password", "forgetPasswordNewPasswordHint": "Please enter your new password", "forgetPasswordConfirmPassword": "Confirm Password", "forgetPasswordConfirmPasswordHint": "Please re-enter your new password", "forgetPasswordReset": "Reset Password", "forgetPasswordSuccess": "Password Reset Successful", "forgetPasswordSuccessHint": "Please use the new password to log in", "forgetPasswordSuccessBack": "Back to Login", "forgetPasswordSuccessBackHome": "Back to Home", "forgetPasswordSuccessBackLogin": "Back to Login", "forgetPasswordSuccessBackRegister": "Back to Register", "forgetPasswordSuccessBackForgetPassword": "Back to Forgot Password", "aboutTitle": "About", "aboutVersion": "Version", "aboutVersionHint": "1.0.0", "aboutUpdate": "Check for Updates", "aboutUpdateHint": "You have the latest version", "aboutUpdateSuccess": "Update Successful", "feedbackTitle": "<PERSON><PERSON><PERSON>", "feedbackHint": "Please leave your valuable comments. We will keep improving our products.", "feedbackContact": "Contact Information", "feedbackContactHint": "Please enter your contact information", "feedbackContentHint": "Please enter your feedback", "feedbackImage": "Upload Image", "feedbackImageHint": "You can upload up to 5 images", "feedbackDevice": "Device Model", "feedbackDeviceHint": "Please select your device model", "feedbackSubmit": "Submit", "feedbackSuccess": "Submission Successful", "feedbackSuccessHint": "Thank you for your feedback. We will handle it as soon as possible", "feedbackType": "Feedback Type", "feedbackTypeRequired": "Please select a feedback type", "feedbackError": "Submission Failed, please try again later", "error": "Error", "logoutTitle": "Log Out", "logoutHint": "Are you sure you want to log out?", "cancel": "Cancel", "confirm": "Confirm", "deviceNotFoundTitle": "Device Not Found?", "deviceNotFoundHint": "1. Please ensure the device is powered on.\n2. Make sure the device is properly connected via Bluetooth.\n3. After completing the above steps, tap the refresh button to search again.\n4. Some phones may need location services enabled for BLE connections. Try enabling location services and refresh again.", "operateDescription": "Operation Instructions", "blueHeadAmp": "Bluetooth Headphone Amplifier", "decoderAmp": "Decoder Amplifier", "headset": "Headset", "bluetoothHeadphoneAmplifier": "Bluetooth & Headphone", "player": "Player", "inputLinker": "Manually Enter Linker Control IP", "connect": "Connected", "disconnected": "Disconnected", "connecting": "Connecting", "disconnecting": "Disconnecting", "disconnect": "Not Connected", "personalCenter": "Personal Center", "editPersonalInfo": "Edit Personal Information", "accountSecurity": "Account Security Settings", "avatar": "Avatar", "gender": "Gender", "secret": "Secret", "male": "Male", "female": "Female", "birthday": "Birthday", "signature": "Signature", "region": "Region", "nickname": "Nickname", "save": "Save", "success": "Success", "updateSuccess": "Update Successful", "userNotFound": "User Not Found", "loginPasswordModify": "Modify Login Password", "accountAndBinding": "Account and Binding Settings", "modifyPhone": "Modify Phone Number", "cancelAccount": "<PERSON><PERSON> Account", "modifyPassword": "Modify Password", "oldPassword": "Old Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "modifyPasswordSuccess": "Password Modified Successfully", "informationIncomplete": "Incomplete Information", "oldPasswordError": "Incorrect Old Password", "newPasswordAndConfirmPasswordNotConsistent": "New Password and Confirm Password Do Not Match", "oldPasswordAndNewPasswordCannotBeTheSame": "Old Password and New Password Cannot Be the Same", "newPasswordCannotBeTheSameAsTheOldPassword": "New Password Cannot Be the Same as the Old Password", "emailBinding": "Email Binding", "notBound": "Not Bound", "bound": "Bound", "nowEmailVerification": "Verify Current Email", "emailVerificationPrompt": "Please enter the email you want to bind. You can later change the binding or recover your password via email.", "email": "Email", "sendVerificationCode": "Send Verification Code", "smsVerificationCode": "SMS Verification Code", "verificationCodeHasBeenSent": "Verification Code Sent", "accountBindingSuccess": "Account Successfully Bound", "emailEmpty": "<PERSON><PERSON>not Be Empty", "invalidEmail": "<PERSON><PERSON><PERSON>", "emailBindSuccess": "<PERSON><PERSON> Successfully", "unbindEmail": "<PERSON><PERSON><PERSON>", "unbindEmailConfirm": "Are you sure you want to unbind the email?", "emailUnbindSuccess": "Email Unbound Successfully", "boundEmail": "Bound Email", "unbind": "Unbind", "bind": "Bind", "getEmailVerificationCode": "Get Email Verification Code", "bindNewPhone": "Bind New Phone", "bindNewPhonePrompt": "Please enter your new phone number and click to receive a verification code for validation.", "bindNewPhoneSuccess": "New Phone Bound Successfully", "bindNewPhoneFailure": "Failed to Bind New Phone", "bindNewPhoneFailurePrompt": "Failed to bind new phone. Please try again later.", "will": "Will", "accountWillBeCancelled": "The linked account will be cancelled", "cancellationInstructions": "Note: Account cancellation is only supported by the account owner and cannot be restored after cancellation. Please proceed cautiously. After formal cancellation, you will no longer be able to use this account, nor retrieve any content or information related to this account, including but not limited to:\n\n1. Personal information under this account (including but not limited to avatar, nickname, bound products, etc.)\n2. All rights under this account (including but not limited to MQA membership, forum points, etc.)\n\n3. All playlists and other privileges under this account\n\n4. Other potential consequences resulting from account cancellation", "cancellation": "Cancel", "confirmCancellation": "Are you sure you want to cancel the account?", "cancellationSuccess": "Cancellation Successful", "cancellationFailure": "Cancellation Failed", "exitApp": "Exit App", "exitAppPrompt": "Are you sure you want to exit the app?", "phoneUpdateSuccess": "Phone Number Modified Successfully", "phoneEmpty": "Phone Number Cannot Be Empty", "verificationCodeEmpty": "Verification Code Cannot Be Empty", "samePhoneNumber": "New Phone Number Cannot Be the Same as the Original Phone Number", "verifyOldPhone": "Verify Original Phone Number", "setNewPhone": "Set New Phone Number", "oldPhone": "Original Phone Number", "newPhone": "New Phone Number", "passwordTooShort": "Password Too Short", "passwordEmpty": "Password Cannot Be Empty", "passwordUpdateSuccess": "Password Modified Successfully", "passwordUpdateSuccessRelogin": "Password Modified Successfully, please log in again", "scanning": "Scanning", "scanningDevicesHint": "Scanning for nearby Bluetooth devices...", "startScanningHint": "Start Scanning", "manualInputIP": "Manual Input IP", "manualInputIPHint": "Please enter the Linker control IP", "bluetoothAndDac": "Bluetooth & DAC", "streamer": "Player", "pleaseInputIP": "Please enter the IP", "deviceNotFoundHint1": "1. Please ensure the device is powered on.", "deviceNotFoundHint2": "2. Please keep the device properly connected to your phone''s Bluetooth", "deviceNotFoundHint3": "3. After completing the above steps, click the refresh button to search again.", "deviceNotFoundHint4": "4. Some phone systems may require enabling the location service for Bluetooth BLE connections. You can try enabling the location service and refreshing again.", "invalidQRCode": "Invalid QR Code", "scanQRCode": "Scan QR Code", "scanQRCodeHint": "Please scan the QR code", "scanQRCodeBottomHint": "Place the QR code in the box to scan automatically", "noDevicesFound": "No Devices Found", "discoveredDevices": "Discovered Devices", "discoveredDevicesHint": "Found Multiple Discovered Devices", "tapToConnect": "Tap to Connect", "availableDevices": "Available Devices", "languageCode": "en", "deviceNotFound": "Device Not Found", "connected": "Connected", "battery": "Battery", "tryAgain": "Try Again", "volumeControl": "Volume Control", "unlock": "Unlock", "lock": "Lock", "audioSettings": "Audio Settings", "displayView": "Display View", "low": "Low", "middle": "Mid", "high": "High", "equalizer": "Equalizer", "eqPreset": "EQ Preset", "digitalFilter": "Digital Filter", "channelBalance": "Channel Balance", "dsdMode": "DSD Mode", "systemSettings": "System Settings", "displayBrightness": "Display Brightness", "displayTimeout": "Display Timeout", "autoPowerOff": "Auto Power Off", "disabled": "Disabled", "ledIndicator": "LED Indicator", "connectToAccessSettings": "Connect to Access Settings", "connectionInfo": "Connection Information", "codec": "Codec", "deviceInfo": "Device Information", "firmwareVersion": "Firmware Version", "serialNumber": "Serial Number", "totalPlayTime": "Total Play Time", "deleteDevice": "Delete Device", "deleteDeviceConfirm": "Are you sure you want to delete the device?", "delete": "Delete", "preamplifier": "Preamplifier", "decodeModeDac": "DAC", "displayNormal": "Normal", "displayVu": "VU", "displayFft": "FFT", "inputUsb": "USB", "inputOptical": "Optical", "inputCoaxial": "Coaxial", "inputBluetooth": "Bluetooth", "languageZh": "Chinese", "languageEn": "English", "headphoneGainHigh": "High Gain", "headphoneGainLow": "<PERSON>ain", "multiFunctionKeyInputSelect": "Input Select", "multiFunctionKeyLineOutSelect": "Line Out Select", "multiFunctionKeyHeadphoneOutSelect": "Headphone Out Select", "multiFunctionKeyHomeSelect": "Home Select", "multiFunctionKeyBrightnessSelect": "Brightness Select", "multiFunctionKeySleep": "Sleep", "multiFunctionKeyPcmFilterSelect": "PCM Filter Select", "multiFunctionKeyMute": "Mute", "multiFunctionKeyPeqSelect": "PEQ Select", "outputClose": "Off", "outputSingleEnded": "RCA", "outputBalanced": "XLR", "outputSingleEndedAndBalanced": "RCA+XLR", "powerTriggerSignal": "Signal", "powerTriggerVoltage": "12V", "powerTriggerClose": "Off", "screenBrightnessHigh": "High", "screenBrightnessMedium": "Medium", "screenBrightnessLow": "Low", "screenBrightnessAuto": "Auto", "themeAurora": "Aurora", "themeOrange": "Orange", "themePeru": "Peru", "themeGreen": "<PERSON>", "themeKhaki": "Dark Khaki", "themeRose": "<PERSON>", "themeBlue": "Blue", "themePurple": "Fantasy Purple", "themeWhite": "White", "darkMode": "Dark Mode", "lightMode": "Light Mode", "darkModeDescription": "Currently using dark theme", "lightModeDescription": "Currently using light theme", "usbTypeUac2": "UAC 2.0", "usbTypeUac1": "UAC 1.0", "deviceTypeBluetooth": "Bluetooth", "deviceTypeDac": "DAC", "deviceTypeHeadphone": "Headphone", "deviceTypePlayer": "Player", "name": "Name", "volume": "Volume", "headphoneOutput": "Headphone Output", "headphoneGain": "Headphone Gain", "inputSelect": "Input Select", "outputSelect": "Output Select", "advanced": "Advanced", "advancedSettings": "Advanced Settings", "editName": "Edit Name", "enterNewName": "Enter New Name", "setting": "Setting", "peq": "PEQ", "guide": "Guide", "theme": "Theme", "powerTrigger": "Power Trigger", "audioBalance": "Audio Balance", "filter": "Filter", "decodeMode": "Decode Mode", "audioMonitoring": "Audio Bluetooth", "bluetoothAptx": "Bluetooth APTX", "relay": "Remote Control", "multiFunctionKey": "Custom Multi-function Button", "usbMode": "USB Mode", "screenBrightness": "Screen Brightness", "language": "Language", "resetSettings": "Reset Advanced Settings", "restoreFactorySettings": "Restore Factory Settings", "factoryReset": "Factory Reset", "factoryResetConfirm": "Are you sure you want to restore the factory settings?", "channel": "Channel", "resetSettingsConfirmation": "Are you sure you want to reset the advanced settings?", "restoreFactorySettingsConfirmation": "Are you sure you want to restore the factory settings?", "displaySettings": "Display Settings", "inputSettings": "Input Settings", "outputSettings": "Output Settings", "functions": "Functions", "import": "Import", "export": "Export", "target": "Target", "sourceFR": "Source FR", "eachFilter": "Each Filter", "combinedFilter": "Combined Filter", "filteredFR": "Filtered FR", "raw": "Raw", "compensated": "Comp", "preAmplification": "Preamp", "gain": "<PERSON><PERSON>", "peakingFilter": "Peaking Filter", "lowPassFilter": "Low-Pass Filter", "highPassFilter": "High-Pass Filter", "lowShelfFilter": "Low-<PERSON><PERSON>", "highShelfFilter": "High-<PERSON><PERSON>", "centerFrequency": "Center Fre", "connerFrequency": "Corner Frequency", "q": "Q Factor", "frequency": "Frequency", "type": "Type", "addFilter": "Add Filter", "mode": "Mode", "import_target": "Import Target", "import_sourceFR": "Import Source FR", "selectDataFile": "Select Data File", "feedbackContent": "Feedback Content", "problemType": "Problem Type", "selectDeviceType": "Select Device Type", "device": "<PERSON><PERSON>", "addPicture": "Add Picture (Optional)", "contact": "Contact", "feedbackTypeFeatureSuggestion": "Feature Suggestion", "feedbackTypeBugReport": "Bug Report", "feedbackTypeUIImprovement": "UI Improvement", "feedbackTypeOther": "Other", "checkUpdate": "Check for Updates", "checking": "Checking...,", "alreadyLatestVersion": "Already Latest Version", "failed": "Check update failed", "foundNewVersion": "New Version Available", "currentVersion": "Current Version", "newVersion": "New Version", "updateContent": "Update Content:", "later": "Later", "updateNow": "Update Now", "downloading": "Downloading...", "downloadCompleted": "Download completed", "downloadFailed": "Download failed", "storagePermissionDenied": "Storage permission denied", "cannotGetDownloadPath": "Cannot get download path", "checkUpdateFailed": "Check update failed", "installPermissionDenied": "Install permission denied", "failedToLoadContent": "Failed to load content", "lastUpdated": "Last Updated", "termsAndConditions": "Terms and Conditions", "agreementDialogContent": "By clicking Agree, you agree to the User Agreement and Privacy Policy", "userAgreement": "User Agreement", "privacyPolicyText": "Privacy Policy", "disagree": "Disagree", "exportSuccess": "Export Successful", "fileSavedTo": "File saved to:", "fileContentPreview": "File content preview:", "ok": "OK", "openFileLocation": "Open File Location", "sharedFilterFile": "Shared Filter File", "sharedFile": "Shared File", "cannotOpenFileOrItsDirectory": "Cannot open file or its directory", "errorOpeningFileLocation": "Error opening file location:", "errorSharingFile": "Error sharing file", "cannotParseSelectedFile": "Cannot parse selected file. Please ensure it is a valid CSV format and contains frequency and response data.", "cannotExtractValidFrequencyAndResponseDataFromCSV": "Cannot extract valid frequency and response data from CSV.", "importSuccess": "Import Successful", "successfullyImportedFile": "Successfully imported file", "errorImportingFile": "Error importing file", "noDataToExport": "No data to export.", "fileSavedToAppFolder": "File saved to app folder:", "errorSavingFile": "Error saving file:", "saveMergedFilterFile": "Save Merged Filter File", "startUsing": "Start Using", "nextPage": "Next Page", "background": "Background", "confirmDelete": "Confirm Delete", "confirmDeleteHint": "Are you sure you want to delete this background image?", "opacity": "Opacity", "blur": "Blur", "selectFromAlbum": "Select from Album", "takePhoto": "Take Photo", "addBackground": "Add Background", "deleteBackgroundFailed": "Failed to delete background", "saveBackgroundFailed": "Failed to save background", "errorExportingFile": "Error exporting file", "skip": "<PERSON><PERSON>", "version": "Version", "copyright": " 2025 Topping. All rights reserved.", "newFirmwareAvailable": "New Firmware Available", "tip": "Tip", "firmwareUpgrade": "Firmware Upgrade", "firmwareUpgrading": "Upgrading Firmware", "firmwareUpgradeSuccess": "Firmware Upgrade Success", "firmwareUpgradeFailed": "Firmware Upgrade Failed", "firmwareUpgradeConfirm": "Are you sure to upgrade firmware?", "firmwareUpgradeWarning": "Please do not disconnect the device during upgrade", "firmwareSize": "Firmware Size", "firmwareDescription": "Description", "firmwareForceUpdate": "Force Update", "firmwareDownloading": "Downloading Firmware", "firmwareInstalling": "Installing Firmware", "settingResetFail": "Reset Failed", "@settingResetFail": {}, "firmwareUpdateTitle": "Firmware Update", "@firmwareUpdateTitle": {"description": "Title for the firmware update page"}, "firmwareUpdateNewFirmwareFound": "New Firmware Found", "@firmwareUpdateNewFirmwareFound": {}, "firmwareUpdateFirmwareName": "Firmware Name", "@firmwareUpdateFirmwareName": {}, "firmwareUpdateVersion": "Version", "@firmwareUpdateVersion": {}, "firmwareUpdateDeviceModel": "Device Model", "@firmwareUpdateDeviceModel": {}, "firmwareUpdateFileSize": "File Size", "@firmwareUpdateFileSize": {}, "firmwareUpdateDescription": "Update Notes", "@firmwareUpdateDescription": {}, "firmwareUpdateMandatoryUpdateNote": "* This version is a mandatory update", "@firmwareUpdateMandatoryUpdateNote": {}, "firmwareUpdateStatus": "Update Status", "@firmwareUpdateStatus": {}, "firmwareUpdateDownloading": "Downloading", "@firmwareUpdateDownloading": {}, "firmwareUpdateUpgrading": "Upgrading", "@firmwareUpdateUpgrading": {}, "firmwareUpdateDoNotDisconnect": "Please do not disconnect or turn off the device", "@firmwareUpdateDoNotDisconnect": {}, "firmwareUpdateReadyToUpdate": "Ready to Update", "@firmwareUpdateReadyToUpdate": {}, "firmwareUpdateCancel": "Cancel Update", "@firmwareUpdateCancel": {}, "firmwareUpdateStart": "Start Update", "@firmwareUpdateStart": {}, "firmwareUpdateLatestVersion": "Already Latest Version", "@firmwareUpdateLatestVersion": {}, "firmwareUpdateNoNeed": "No firmware update needed", "@firmwareUpdateNoNeed": {}, "firmwareUpdateBack": "Back", "@firmwareUpdateBack": {}, "firmwareUpdateSuccessTitle": "Update Successful", "@firmwareUpdateSuccessTitle": {}, "firmwareUpdateSuccessMessage": "Device firmware updated to the latest version", "@firmwareUpdateSuccessMessage": {}, "firmwareUpdateSuccessRebootMessage": "Upgrade successful, device is rebooting...", "@firmwareUpdateSuccessRebootMessage": {"description": "Message shown when firmware update succeeds and the device disconnects (likely rebooting)"}, "firmwareUpdateDownloadingTitle": "Downloading", "@firmwareUpdateDownloadingTitle": {}, "firmwareUpdateDownloadingMessage": "Downloading firmware file...", "@firmwareUpdateDownloadingMessage": {}, "firmwareUpdateErrorTitle": "Error", "@firmwareUpdateErrorTitle": {}, "firmwareUpdateDownloadFailed": "Firmware download failed", "@firmwareUpdateDownloadFailed": {}, "firmwareUpdateUpgradingTitle": "Upgrading", "@firmwareUpdateUpgradingTitle": {}, "firmwareUpdateUpgradingMessage": "Starting firmware upgrade...", "@firmwareUpdateUpgradingMessage": {}, "firmwareUpdateSetDeviceFailed": "Set device failed", "@firmwareUpdateSetDeviceFailed": {}, "firmwareUpdateErrorDuringUpdate": "Error during upgrade: {error}", "@firmwareUpdateErrorDuringUpdate": {"placeholders": {"error": {"type": "String", "example": "Connection failed"}}}, "firmwareUpdateCancelledTitle": "Cancelled", "@firmwareUpdateCancelledTitle": {}, "firmwareUpdateCancelledMessage": "Firmware upgrade cancelled", "@firmwareUpdateCancelledMessage": {}, "commonConfirm": "Confirm", "@commonConfirm": {}, "checkFirmwareUpdate": "Check Firmware Update", "@checkFirmwareUpdate": {"description": "Text for checking for firmware updates"}, "deviceRebootTitle": "<PERSON><PERSON> Reboot", "@deviceRebootTitle": {"description": "Title for device reboot notification"}, "deviceRebootMessage": "The device is restarting, please wait...", "@deviceRebootMessage": {"description": "Message content for device reboot notification"}, "infoTitle": "Information", "@infoTitle": {"description": "Title for information dialog"}, "errorOpeningFile": "Error opening file: {error}", "@errorOpeningFile": {"placeholders": {"error": {"type": "String", "example": "File not found"}}}, "fileExported": "File exported", "@fileExported": {"description": "Message shown when a file has been exported"}, "shareFile": "Share File", "@shareFile": {"description": "Button text for sharing a file"}, "eqWavePainterStarted": "EQ Wave Painter started. Size: {size}", "@eqWavePainterStarted": {"placeholders": {"size": {"type": "String", "example": "Size(360.0, 240.0)"}}}, "targetRawDataPrepared": "Target raw data prepared (interpolated): {count} points", "@targetRawDataPrepared": {"placeholders": {"count": {"type": "int", "example": "200"}}}, "sourceFRRawDataPrepared": "Source FR raw data prepared (interpolated): {count} points", "@sourceFRRawDataPrepared": {"placeholders": {"count": {"type": "int", "example": "200"}}}, "combinedRawDataPrepared": "Combined raw data prepared: {count} points", "@combinedRawDataPrepared": {"placeholders": {"count": {"type": "int", "example": "200"}}}, "individualBandsRawDataPrepared": "Individual bands raw data prepared: {bandCount} bands, total points: {pointCount}", "@individualBandsRawDataPrepared": {"placeholders": {"bandCount": {"type": "int", "example": "5"}, "pointCount": {"type": "int", "example": "1000"}}}, "filteredFRRawDataPrepared": "Filtered FR raw data prepared (based on interpolated source): {count} points", "@filteredFRRawDataPrepared": {"placeholders": {"count": {"type": "int", "example": "200"}}}, "dynamicDbRangeCalculated": "Dynamic dB range calculated: Min={min}, Max={max}", "@dynamicDbRangeCalculated": {"placeholders": {"min": {"type": "String", "example": "-30.0"}, "max": {"type": "String", "example": "30.0"}}}, "coordinateConverterCreated": "Coordinate converter created and chart height adjusted", "@coordinateConverterCreated": {"description": "Log message when coordinate converter is created"}, "gridAndLabelsDrawn": "Grid and labels drawn", "@gridAndLabelsDrawn": {"description": "Log message when grid and labels are drawn"}, "drawingTargetCurve": "Drawing Target Curve...", "@drawingTargetCurve": {"description": "Log message when drawing target curve"}, "drawingSourceFRCurve": "Drawing Source FR Curve...", "@drawingSourceFRCurve": {"description": "Log message when drawing source FR curve"}, "drawingIndividualBands": "Drawing Individual Bands...", "@drawingIndividualBands": {"description": "Log message when drawing individual bands"}, "drawingCombinedCurve": "Drawing Combined Curve...", "@drawingCombinedCurve": {"description": "Log message when drawing combined curve"}, "drawingFilteredFRCurve": "Drawing Filtered FR Curve...", "@drawingFilteredFRCurve": {"description": "Log message when drawing filtered FR curve"}, "drawingFlatLine": "Drawing Flat Line...", "@drawingFlatLine": {"description": "Log message when drawing flat line"}, "eqWavePainterFinished": "EQ Wave Painter finished", "@eqWavePainterFinished": {"description": "Log message when EQ wave painter finishes"}, "finalDynamicDbRange": "Final Dynamic dB range: Min={min}, Max={max}", "@finalDynamicDbRange": {"placeholders": {"min": {"type": "String", "example": "-30.0"}, "max": {"type": "String", "example": "30.0"}}}, "peqSettings": "PEQ Settings", "@peqSettings": {"description": "PEQ settings page title"}, "importExport": "Import & Export", "@importExport": {"description": "Import and export section header"}, "configManagement": "Configuration Management", "@configManagement": {"description": "Configuration management section header"}, "addConfig": "Add Configuration", "@addConfig": {"description": "Add configuration dialog title"}, "configName": "Configuration Name", "@configName": {"description": "Configuration name input label"}, "description": "Description (Optional)", "@description": {"description": "Description input label"}, "configNameHint": "e.g. <PERSON> Boost, Clear Vocals, etc.", "@configNameHint": {"description": "Configuration name hint text"}, "configDescriptionHint": "Briefly describe the purpose of this configuration", "@configDescriptionHint": {"description": "Configuration description hint text"}, "@cancel": {"description": "Cancel button text"}, "add": "Add", "@add": {"description": "Add button text"}, "@save": {"description": "Save button text"}, "@delete": {"description": "Delete button text"}, "addNewConfig": "Add New Configuration", "@addNewConfig": {"description": "Add new configuration tooltip"}, "importTarget": "Import Target", "@importTarget": {"description": "Import target button text"}, "importSourceFR": "Import Source FR", "@importSourceFR": {"description": "Import source FR button text"}, "exportCombinedFilter": "Export Combined Filter", "@exportCombinedFilter": {"description": "Export combined filter button text"}, "targetFR": "Target FR", "@targetFR": {"description": "Target frequency response label"}, "@sourceFR": {"description": "Source frequency response label"}, "editConfig": "Edit Configuration", "@editConfig": {"description": "Edit configuration dialog title/tooltip"}, "deleteConfig": "Delete Configuration", "@deleteConfig": {"description": "Delete configuration dialog title/tooltip"}, "deleteConfigConfirmation": "Are you sure you want to delete the configuration \"{name}\"? This action cannot be undone.", "@deleteConfigConfirmation": {"description": "Delete configuration confirmation message", "placeholders": {"name": {}}}, "deleteTargetFile": "Delete Target File", "@deleteTargetFile": {"description": "Delete target file dialog title"}, "deleteSourceFRFile": "Delete Source FR File", "@deleteSourceFRFile": {"description": "Delete source FR file dialog title"}, "deleteFileConfirmation": "Are you sure you want to delete the file \"{name}\"? This action cannot be undone.", "@deleteFileConfirmation": {"description": "Delete file confirmation message", "placeholders": {"name": {}}}, "noConfigsMessage": "No configurations yet. Click \"+\" to create a new one.", "@noConfigsMessage": {"description": "Message shown when there are no configurations"}, "devicePowerOff": "Device is powered off", "@devicePowerOff": {"description": "Device is powered off"}, "devicePowerOffHint": "Turn on the power to control the device", "@devicePowerOffHint": {"description": "Turn on the power to control the device"}, "powerOn": "Power On", "@powerOn": {"description": "Power on button text"}, "inputOptical1": "Optical 1", "@inputOptical1": {"description": "Optical 1 input"}, "inputOptical2": "Optical 2", "@inputOptical2": {"description": "Optical 2 input"}, "inputCoaxial1": "Coaxial 1", "@inputCoaxial1": {"description": "Coaxial 1 input"}, "inputCoaxial2": "Coaxial 2", "@inputCoaxial2": {"description": "Coaxial 2 input"}, "inputAes": "AES", "@inputAes": {"description": "AES input"}, "inputIis": "IIS", "@inputIis": {"description": "IIS input"}, "outputDac": "DAC", "@outputDac": {"description": "DAC output"}, "outputPreamp": "Preamp", "@outputPreamp": {"description": "Preamp output"}, "outputAll": "All", "@outputAll": {"description": "All outputs"}, "auto": "Auto", "@auto": {"description": "Auto mode"}, "enabled": "Enabled", "@enabled": {"description": "Enabled state"}, "@disabled": {"description": "Disabled state"}, "standard": "Standard", "@standard": {"description": "Standard mode"}, "inverted": "Inverted", "@inverted": {"description": "Inverted mode"}, "swapped": "Swapped", "@swapped": {"description": "Swapped mode"}, "displaySignal": "Signal", "@displaySignal": {"description": "Signal display"}, "display12V": "12V", "@display12V": {"description": "12V display"}, "displayOff": "Off", "@displayOff": {"description": "Display off"}, "@multiFunctionKeyPeqSelect": {"description": "PEQ select function key"}, "usbTypeC": "Type-C", "@usbTypeC": {"description": "USB Type-C"}, "usbTypeB": "Type-B", "@usbTypeB": {"description": "USB Type-B"}, "powerTriggerOff": "Off", "@powerTriggerOff": {"description": "Power trigger off"}, "powerTrigger12V": "12V", "@powerTrigger12V": {"description": "Power trigger 12V"}, "multiFunctionKeyOutputSelect": "Output Select", "@multiFunctionKeyOutputSelect": {"description": "Output select function key"}, "multiFunctionKeyScreenOff": "Screen Off", "@multiFunctionKeyScreenOff": {"description": "Screen off function key"}, "usbSelect": "USB Select", "usbDsdPassthrough": "USB DSD Passthrough", "iisPhase": "IIS Phase", "iisDsdChannel": "IIS DSD Channel"}