// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get appName => 'トピンホーム';

  @override
  String get homeTitle => 'マイ製品';

  @override
  String get homeAddHint => '製品を追加して、より良い操作体験をお楽しみください';

  @override
  String get addDevice => 'デバイスを追加';

  @override
  String get noDevice => 'デバイスなし';

  @override
  String get addDeviceHint => '製品を追加して、より良い操作体験をお楽しみください';

  @override
  String get foundDevice => 'デバイスを発見';

  @override
  String get devices => 'デバイス';

  @override
  String get loginClick => 'タップしてログイン';

  @override
  String get about => '概要';

  @override
  String get quickStart => 'クイックスタート';

  @override
  String get feedback => 'フィードバック';

  @override
  String get customBackground => '背景のカスタマイズ';

  @override
  String get logout => 'ログアウト';

  @override
  String get registerTitle => '登録';

  @override
  String get phone => '電話番号';

  @override
  String get phoneHint => '電話番号を入力してください';

  @override
  String get verifyCode => '認証コード';

  @override
  String get getSmsVerifyCode => 'SMSコードを取得';

  @override
  String get agreement => '利用規約とプライバシーポリシーに同意します';

  @override
  String get agreementLink => '《利用規約》';

  @override
  String get agree => '同意';

  @override
  String get privacyPolicy => '《プライバシーポリシー》';

  @override
  String get register => '登録';

  @override
  String get login => 'ログイン';

  @override
  String get loginTitle => 'ログイン';

  @override
  String get loginPhoneHint => '電話番号を入力してください';

  @override
  String get loginVerifyCode => '認証コード';

  @override
  String get loginGetSmsVerifyCode => 'SMSコードを取得';

  @override
  String get loginPassword => 'パスワードでログイン';

  @override
  String get loginForgetPassword => 'パスワードをお忘れですか';

  @override
  String get loginFail => 'ログイン失敗';

  @override
  String get passwordError => 'パスワードエラー';

  @override
  String get phoneNotRegistered => '登録されていない電話番号';

  @override
  String get noAccount => 'アカウントをお持ちでないですか？';

  @override
  String get registerNow => '今すぐ登録';

  @override
  String get tips => 'ヒント';

  @override
  String get graphicCodeHint => '画像認証コードを入力してください';

  @override
  String get agreeToTermsHint => '利用規約とプライバシーポリシーに同意してください';

  @override
  String get verificationCodeHint => '認証コードを入力してください';

  @override
  String get usernameHint => 'ユーザー名を入力してください';

  @override
  String get passwordHint => 'パスワードを入力してください';

  @override
  String get confirmPasswordHint => 'パスワードを再入力してください';

  @override
  String get passwordMismatch => 'パスワードが一致しません';

  @override
  String get registerSuccess => '登録成功';

  @override
  String get registerSuccessHint => '電話番号とパスワードでログインしてください';

  @override
  String get registerFailed => '登録失敗';

  @override
  String get getVerificationCode => '認証コードを取得';

  @override
  String get verificationCodeSent => '認証コードが送信されました';

  @override
  String get next => '次へ';

  @override
  String get secondResend => '秒後に再送信';

  @override
  String get settingAccount => 'アカウント設定';

  @override
  String get registerComplete => '登録完了';

  @override
  String get username => 'ユーザー名';

  @override
  String get password => 'パスワード';

  @override
  String get confirmPassword => 'パスワード確認';

  @override
  String get inputError => '入力エラー';

  @override
  String get inputCannotBeEmpty => '入力は空にできません';

  @override
  String get invalidCaptcha => '認証コードエラー';

  @override
  String get forgetPassword => 'パスワードをお忘れの方';

  @override
  String get forgetPasswordTitle => 'パスワードを忘れた';

  @override
  String get forgetPasswordPhoneTitle => '電話番号を入力してください';

  @override
  String get forgetPasswordPhoneHint => '登録済みの電話番号で認証コードを受け取り、次のステップに進みます';

  @override
  String get forgetPasswordVerifyCode => '認証コード';

  @override
  String get forgetPasswordGetSmsVerifyCode => 'SMSコードを取得';

  @override
  String get forgetPasswordNext => '次へ';

  @override
  String get forgetPasswordNewPassword => '新しいパスワード';

  @override
  String get forgetPasswordNewPasswordHint => '新しいパスワードを入力してください';

  @override
  String get forgetPasswordConfirmPassword => 'パスワード確認';

  @override
  String get forgetPasswordConfirmPasswordHint => '新しいパスワードを再入力してください';

  @override
  String get forgetPasswordReset => 'パスワードをリセット';

  @override
  String get forgetPasswordSuccess => 'パスワードのリセット成功';

  @override
  String get forgetPasswordSuccessHint => '新しいパスワードでログインしてください';

  @override
  String get forgetPasswordSuccessBack => 'ログインに戻る';

  @override
  String get forgetPasswordSuccessBackHome => 'ホームに戻る';

  @override
  String get forgetPasswordSuccessBackLogin => 'ログインに戻る';

  @override
  String get forgetPasswordSuccessBackRegister => '登録に戻る';

  @override
  String get forgetPasswordSuccessBackForgetPassword => 'パスワード忘れに戻る';

  @override
  String get aboutTitle => '概要';

  @override
  String get aboutVersion => 'バージョン';

  @override
  String get aboutVersionHint => '1.0.0';

  @override
  String get aboutUpdate => '更新を確認';

  @override
  String get aboutUpdateHint => '最新のバージョンです';

  @override
  String get aboutUpdateSuccess => '更新成功';

  @override
  String get feedbackTitle => 'フィードバック';

  @override
  String get feedbackHint => 'ご意見をいただければ、製品の改善に努めます';

  @override
  String get feedbackContact => '連絡先';

  @override
  String get feedbackContactHint => '連絡先を入力してください';

  @override
  String get feedbackContentHint => 'フィードバック内容を入力してください';

  @override
  String get feedbackImage => '画像をアップロード';

  @override
  String get feedbackImageHint => '最大5枚までアップロード可能';

  @override
  String get feedbackDevice => 'デバイスモデル';

  @override
  String get feedbackDeviceHint => 'デバイスモデルを選択してください';

  @override
  String get feedbackSubmit => '送信';

  @override
  String get feedbackSuccess => '送信完了';

  @override
  String get feedbackSuccessHint => 'フィードバックありがとうございます。できるだけ早く対応いたします';

  @override
  String get feedbackType => 'フィードバックタイプ';

  @override
  String get feedbackTypeRequired => 'フィードバックタイプを選択してください';

  @override
  String get feedbackError => '送信失敗、後でもう一度お試しください';

  @override
  String get error => 'エラー';

  @override
  String get logoutTitle => 'ログアウト';

  @override
  String get logoutHint => 'ログアウトしますか？';

  @override
  String get cancel => 'キャンセル';

  @override
  String get confirm => '確認';

  @override
  String get deviceNotFoundTitle => 'デバイスが見つかりませんか？';

  @override
  String get deviceNotFoundHint => '1. デバイスの電源が入っているか確認してください\n2. デバイスとスマートフォンのBluetooth接続を確認してください\n3. 上記の手順を完了したら、更新ボタンをタップして再検索してください\n4. 一部のスマートフォンではBluetooth BLE接続に位置情報の有効化が必要な場合があります。位置情報を有効にしてから再度更新してください';

  @override
  String get operateDescription => '操作説明';

  @override
  String get blueHeadAmp => 'Bluetoothヘッドフォンアンプ';

  @override
  String get decoderAmp => 'デコーダーアンプ';

  @override
  String get headset => 'ヘッドフォン';

  @override
  String get bluetoothHeadphoneAmplifier => 'BluetoothとヘッドフォンアンプリファイアR';

  @override
  String get player => 'プレーヤー';

  @override
  String get inputLinker => 'Linker制御IPを手動入力';

  @override
  String get connect => '接続済み';

  @override
  String get disconnected => '切断済み';

  @override
  String get connecting => '接続中';

  @override
  String get disconnecting => '切断中';

  @override
  String get disconnect => '未接続';

  @override
  String get personalCenter => 'マイページ';

  @override
  String get editPersonalInfo => '個人情報を編集';

  @override
  String get accountSecurity => 'アカウントセキュリティ';

  @override
  String get avatar => 'アバター';

  @override
  String get gender => '性別';

  @override
  String get secret => '非公開';

  @override
  String get male => '男性';

  @override
  String get female => '女性';

  @override
  String get birthday => '誕生日';

  @override
  String get signature => '自己紹介';

  @override
  String get region => '地域';

  @override
  String get nickname => 'ニックネーム';

  @override
  String get save => '保存';

  @override
  String get success => '成功';

  @override
  String get updateSuccess => '更新成功';

  @override
  String get userNotFound => 'ユーザーが見つかりません';

  @override
  String get loginPasswordModify => 'ログインパスワードの変更';

  @override
  String get accountAndBinding => 'アカウントと連携設定';

  @override
  String get modifyPhone => '電話番号の変更';

  @override
  String get cancelAccount => 'アカウント削除';

  @override
  String get modifyPassword => 'パスワード変更';

  @override
  String get oldPassword => '現在のパスワード';

  @override
  String get newPassword => '新しいパスワード';

  @override
  String get confirmNewPassword => '新しいパスワード（確認）';

  @override
  String get modifyPasswordSuccess => 'パスワード変更が完了しました';

  @override
  String get informationIncomplete => '入力情報が不完全です';

  @override
  String get oldPasswordError => '現在のパスワードが間違っています';

  @override
  String get newPasswordAndConfirmPasswordNotConsistent => '新しいパスワードと確認用パスワードが一致しません';

  @override
  String get oldPasswordAndNewPasswordCannotBeTheSame => '現在のパスワードと新しいパスワードは同じにできません';

  @override
  String get newPasswordCannotBeTheSameAsTheOldPassword => '新しいパスワードは現在のパスワードと同じにできません';

  @override
  String get emailBinding => 'メール連携';

  @override
  String get notBound => '未連携';

  @override
  String get bound => '連携済み';

  @override
  String get nowEmailVerification => 'メール認証';

  @override
  String get emailVerificationPrompt => '連携するメールアドレスを入力してください。後でメール変更やパスワード再設定に使用できます';

  @override
  String get email => 'メールアドレス';

  @override
  String get sendVerificationCode => '認証コードを送信';

  @override
  String get smsVerificationCode => 'SMS認証コード';

  @override
  String get verificationCodeHasBeenSent => '認証コードを送信しました';

  @override
  String get accountBindingSuccess => 'アカウント連携完了';

  @override
  String get emailEmpty => 'メールアドレスを入力してください';

  @override
  String get invalidEmail => '無効なメールアドレス形式です';

  @override
  String get emailBindSuccess => 'メール連携完了';

  @override
  String get unbindEmail => 'メール連携解除';

  @override
  String get unbindEmailConfirm => 'メールの連携を解除しますか？';

  @override
  String get emailUnbindSuccess => 'メール連携解除完了';

  @override
  String get boundEmail => '連携済みメール';

  @override
  String get unbind => '連携解除';

  @override
  String get bind => '連携する';

  @override
  String get getEmailVerificationCode => 'メール認証コードを取得';

  @override
  String get bindNewPhone => '新しい電話番号を連携';

  @override
  String get bindNewPhonePrompt => '新しい電話番号を入力し、認証コードを取得して確認してください';

  @override
  String get bindNewPhoneSuccess => '新しい電話番号の連携完了';

  @override
  String get bindNewPhoneFailure => '新しい電話番号の連携に失敗しました';

  @override
  String get bindNewPhoneFailurePrompt => '新しい電話番号の連携に失敗しました。後でもう一度お試しください';

  @override
  String get will => 'は';

  @override
  String get accountWillBeCancelled => '連携されたアカウントを削除';

  @override
  String get cancellationInstructions => '注意：アカウントの削除はご本人のみが行えます。削除後は復元できませんので、よく検討してください。\nアカウント削除後は、このアカウントを使用できなくなり、アカウントに関連するすべての情報も復元できません：\n\n1. このアカウントの個人情報（プロフィール画像、ニックネーム、連携製品など）\n2. このアカウントの特典（MQAメンバーシップ、フォーラムポイントなど）\n\n3. このアカウントのコンテンツ（プレイリストなど）\n\n4. アカウント削除によって生じるその他の結果';

  @override
  String get cancellation => '削除';

  @override
  String get confirmCancellation => 'アカウントを削除しますか？';

  @override
  String get cancellationSuccess => '削除成功';

  @override
  String get cancellationFailure => '削除失敗';

  @override
  String get exitApp => 'アプリを終了';

  @override
  String get exitAppPrompt => 'アプリを終了しますか？';

  @override
  String get phoneUpdateSuccess => '電話番号の更新が完了しました';

  @override
  String get phoneEmpty => '電話番号を入力してください';

  @override
  String get verificationCodeEmpty => '認証コードを入力してください';

  @override
  String get samePhoneNumber => '新しい電話番号が現在の番号と同じです';

  @override
  String get verifyOldPhone => '現在の電話番号を確認';

  @override
  String get setNewPhone => '新しい電話番号を設定';

  @override
  String get oldPhone => '現在の電話番号';

  @override
  String get newPhone => '新しい電話番号';

  @override
  String get passwordTooShort => 'パスワードは6文字以上必要です';

  @override
  String get passwordEmpty => 'パスワードを入力してください';

  @override
  String get passwordUpdateSuccess => 'パスワード更新が完了しました';

  @override
  String get passwordUpdateSuccessRelogin => 'パスワードの更新が完了しました。再度ログインしてください';

  @override
  String get scanning => 'スキャン中';

  @override
  String get scanningDevicesHint => '近くのBluetoothデバイスをスキャンしています...';

  @override
  String get startScanningHint => 'スキャン開始';

  @override
  String get manualInputIP => 'IPを手動入力';

  @override
  String get manualInputIPHint => 'Linker制御IPを入力してください';

  @override
  String get bluetoothAndDac => 'BluetoothとDAC';

  @override
  String get streamer => 'ストリーマー';

  @override
  String get pleaseInputIP => 'IPを入力してください';

  @override
  String get deviceNotFoundHint1 => '1. デバイスの電源が入っていることを確認してください。';

  @override
  String get deviceNotFoundHint2 => '2. デバイスとスマートフォンのBluetooth接続が正常かを確認してください。';

  @override
  String get deviceNotFoundHint3 => '3. 上記の手順を完了したら、更新ボタンをタップして再検索してください。';

  @override
  String get deviceNotFoundHint4 => '4. 一部のスマートフォンではBluetooth BLE接続に位置情報の有効化が必要な場合があります。位置情報を有効にしてから再度更新してください。';

  @override
  String get invalidQRCode => '無効なQRコード';

  @override
  String get scanQRCode => 'QRコードをスキャン';

  @override
  String get scanQRCodeHint => 'デバイスのQRコードをスキャンしてください';

  @override
  String get scanQRCodeBottomHint => 'QRコードを枠内に合わせると自動的にスキャンされます';

  @override
  String get noDevicesFound => 'デバイスが見つかりません';

  @override
  String get discoveredDevices => '接続可能な製品';

  @override
  String get discoveredDevicesHint => '複数の接続可能な製品が見つかりました';

  @override
  String get tapToConnect => 'タップして接続';

  @override
  String get availableDevices => '利用可能なデバイス';

  @override
  String get languageCode => 'ja';

  @override
  String get deviceNotFound => 'デバイスが見つかりません';

  @override
  String get connected => '接続済み';

  @override
  String get battery => 'バッテリー';

  @override
  String get tryAgain => 'もう一度試す';

  @override
  String get volumeControl => '音量調整';

  @override
  String get unlock => 'ロック解除';

  @override
  String get lock => 'ロック';

  @override
  String get audioSettings => 'オーディオ設定';

  @override
  String get displayView => '表示画面';

  @override
  String get low => '低';

  @override
  String get middle => '中';

  @override
  String get high => '高';

  @override
  String get equalizer => 'イコライザー';

  @override
  String get eqPreset => 'EQプリセット';

  @override
  String get digitalFilter => 'デジタルフィルター';

  @override
  String get channelBalance => 'チャンネルバランス';

  @override
  String get dsdMode => 'DSDモード';

  @override
  String get systemSettings => 'システム設定';

  @override
  String get displayBrightness => '画面の明るさ';

  @override
  String get displayTimeout => '画面のタイムアウト';

  @override
  String get autoPowerOff => '自動電源オフ';

  @override
  String get disabled => '無効';

  @override
  String get ledIndicator => 'LEDインジケーター';

  @override
  String get connectToAccessSettings => '設定にアクセスするには接続してください';

  @override
  String get connectionInfo => '接続情報';

  @override
  String get codec => 'コーデック';

  @override
  String get deviceInfo => 'デバイス情報';

  @override
  String get firmwareVersion => 'ファームウェアバージョン';

  @override
  String get serialNumber => 'シリアル番号';

  @override
  String get totalPlayTime => '総再生時間';

  @override
  String get deleteDevice => 'デバイスを削除';

  @override
  String get deleteDeviceConfirm => 'このデバイスを削除しますか？';

  @override
  String get delete => '削除';

  @override
  String get preamplifier => 'プリアンプ';

  @override
  String get decodeModeDac => 'DAC';

  @override
  String get displayNormal => '通常';

  @override
  String get displayVu => 'VUメーター';

  @override
  String get displayFft => 'スペクトラム';

  @override
  String get inputUsb => 'USB';

  @override
  String get inputOptical => '光デジタル';

  @override
  String get inputCoaxial => '同軸デジタル';

  @override
  String get inputBluetooth => 'Bluetooth';

  @override
  String get languageZh => '中国語';

  @override
  String get languageEn => '英語';

  @override
  String get headphoneGainHigh => '高ゲイン';

  @override
  String get headphoneGainLow => '低ゲイン';

  @override
  String get multiFunctionKeyInputSelect => '入力選択';

  @override
  String get multiFunctionKeyLineOutSelect => 'ライン出力選択';

  @override
  String get multiFunctionKeyHeadphoneOutSelect => 'ヘッドフォン出力選択';

  @override
  String get multiFunctionKeyHomeSelect => 'ホーム選択';

  @override
  String get multiFunctionKeyBrightnessSelect => '明るさ選択';

  @override
  String get multiFunctionKeySleep => 'スリープ';

  @override
  String get multiFunctionKeyPcmFilterSelect => 'PCMフィルター選択';

  @override
  String get multiFunctionKeyMute => 'ミュート';

  @override
  String get multiFunctionKeyPeqSelect => 'PEQ選択';

  @override
  String get outputClose => '閉じる';

  @override
  String get outputSingleEnded => 'RCA';

  @override
  String get outputBalanced => 'XLR';

  @override
  String get outputSingleEndedAndBalanced => 'RCA+XLR';

  @override
  String get powerTriggerSignal => 'シグナル';

  @override
  String get powerTriggerVoltage => '12V';

  @override
  String get powerTriggerClose => 'オフ';

  @override
  String get screenBrightnessHigh => '高';

  @override
  String get screenBrightnessMedium => '中';

  @override
  String get screenBrightnessLow => '低';

  @override
  String get screenBrightnessAuto => '自動';

  @override
  String get themeAurora => 'オーロラ';

  @override
  String get themeOrange => 'オレンジ';

  @override
  String get themePeru => 'ペルー';

  @override
  String get themeGreen => 'グリーン';

  @override
  String get themeKhaki => 'カーキ';

  @override
  String get themeRose => 'ローズ';

  @override
  String get themeBlue => 'ブルー';

  @override
  String get themePurple => 'パープル';

  @override
  String get themeWhite => 'ホワイト';

  @override
  String get darkMode => 'ダークモード';

  @override
  String get lightMode => 'ライトモード';

  @override
  String get darkModeDescription => '現在ダークテーマを使用中';

  @override
  String get lightModeDescription => '現在ライトテーマを使用中';

  @override
  String get usbTypeUac2 => 'UAC 2.0';

  @override
  String get usbTypeUac1 => 'UAC 1.0';

  @override
  String get deviceTypeBluetooth => 'Bluetooth';

  @override
  String get deviceTypeDac => 'DAC';

  @override
  String get deviceTypeHeadphone => 'ヘッドフォンアンプ';

  @override
  String get deviceTypePlayer => 'プレーヤー';

  @override
  String get name => '名前';

  @override
  String get volume => '音量';

  @override
  String get headphoneOutput => 'ヘッドフォン出力';

  @override
  String get headphoneGain => 'ヘッドフォンゲイン';

  @override
  String get inputSelect => '入力選択';

  @override
  String get outputSelect => '出力選択';

  @override
  String get advanced => '詳細';

  @override
  String get advancedSettings => '詳細設定';

  @override
  String get editName => '名前を編集';

  @override
  String get enterNewName => '新しい名前を入力してください';

  @override
  String get setting => '設定';

  @override
  String get peq => 'イコライザー';

  @override
  String get guide => '操作ガイド';

  @override
  String get theme => 'テーマ';

  @override
  String get powerTrigger => '電源トリガー';

  @override
  String get audioBalance => 'オーディオバランス';

  @override
  String get filter => 'フィルター';

  @override
  String get decodeMode => 'デコードモード';

  @override
  String get audioMonitoring => 'オーディオBluetooth';

  @override
  String get bluetoothAptx => 'Bluetooth aptX';

  @override
  String get relay => 'リモコン';

  @override
  String get multiFunctionKey => '多機能キーのカスタマイズ';

  @override
  String get usbMode => 'USB';

  @override
  String get screenBrightness => '画面の明るさ';

  @override
  String get language => '言語';

  @override
  String get resetSettings => '詳細設定をリセット';

  @override
  String get restoreFactorySettings => '工場出荷時設定に戻す';

  @override
  String get factoryReset => 'Factory Reset';

  @override
  String get factoryResetConfirm => 'Are you sure you want to restore the factory settings?';

  @override
  String get channel => 'チャンネル';

  @override
  String get resetSettingsConfirmation => '詳細設定をリセットしますか？';

  @override
  String get restoreFactorySettingsConfirmation => '工場出荷時設定に戻しますか？';

  @override
  String get displaySettings => 'Display Settings';

  @override
  String get inputSettings => 'Input Settings';

  @override
  String get outputSettings => 'Output Settings';

  @override
  String get functions => 'Functions';

  @override
  String get import => 'インポート';

  @override
  String get export => 'エクスポート';

  @override
  String get target => 'ターゲット';

  @override
  String get sourceFR => 'ソースFR';

  @override
  String get eachFilter => '各フィルター';

  @override
  String get combinedFilter => '結合フィルター';

  @override
  String get filteredFR => 'フィルター後の周波数応答';

  @override
  String get raw => '生信号';

  @override
  String get compensated => '補正';

  @override
  String get preAmplification => 'プリアンプ';

  @override
  String get gain => 'G値';

  @override
  String get peakingFilter => 'ピーキングフィルター';

  @override
  String get lowPassFilter => 'ローパスフィルター';

  @override
  String get highPassFilter => 'ハイパスフィルター';

  @override
  String get lowShelfFilter => 'ローシェルフフィルター';

  @override
  String get highShelfFilter => 'ハイシェルフフィルター';

  @override
  String get centerFrequency => '中心周波数';

  @override
  String get connerFrequency => 'コーナー周波数';

  @override
  String get q => 'Q値';

  @override
  String get frequency => '周波数';

  @override
  String get type => '種類';

  @override
  String get addFilter => 'フィルターを追加';

  @override
  String get mode => 'モード';

  @override
  String get import_target => 'ターゲットカーブのインポート';

  @override
  String get import_sourceFR => 'ソース周波数応答カーブのインポート';

  @override
  String get selectDataFile => 'データファイルを選択';

  @override
  String get feedbackContent => 'フィードバック内容';

  @override
  String get problemType => '問題の種類';

  @override
  String get selectDeviceType => 'デバイスタイプを選択';

  @override
  String get device => 'デバイス';

  @override
  String get addPicture => '画像を追加';

  @override
  String get contact => '連絡先';

  @override
  String get feedbackTypeFeatureSuggestion => '機能の提案';

  @override
  String get feedbackTypeBugReport => 'バグ報告';

  @override
  String get feedbackTypeUIImprovement => 'UI改善';

  @override
  String get feedbackTypeOther => 'その他';

  @override
  String get checkUpdate => '更新を確認';

  @override
  String get checking => '確認中...';

  @override
  String get alreadyLatestVersion => '最新バージョンです';

  @override
  String get failed => 'Check update failed';

  @override
  String get foundNewVersion => '新しいバージョンが見つかりました';

  @override
  String get currentVersion => '現在のバージョン';

  @override
  String get newVersion => '新しいバージョン';

  @override
  String get updateContent => '更新内容';

  @override
  String get later => '後で';

  @override
  String get updateNow => '今すぐ更新';

  @override
  String get downloading => 'ダウンロード中...';

  @override
  String get downloadCompleted => 'ダウンロード完了';

  @override
  String get downloadFailed => 'ダウンロード失敗';

  @override
  String get storagePermissionDenied => 'ストレージアクセス権限が拒否されました';

  @override
  String get cannotGetDownloadPath => 'ダウンロードパスを取得できません';

  @override
  String get checkUpdateFailed => '更新の確認に失敗しました';

  @override
  String get installPermissionDenied => 'インストール権限が拒否されました';

  @override
  String get failedToLoadContent => 'コンテンツの読み込みに失敗しました';

  @override
  String get lastUpdated => '最終更新';

  @override
  String get termsAndConditions => '利用規約';

  @override
  String get agreementDialogContent => 'アプリを使用する前に、利用規約とプライバシーポリシーに同意してください';

  @override
  String get userAgreement => '利用規約';

  @override
  String get privacyPolicyText => 'プライバシーポリシー';

  @override
  String get disagree => '同意しない';

  @override
  String get exportSuccess => 'エクスポートに成功しました';

  @override
  String get fileSavedTo => 'ファイルの保存先：';

  @override
  String get fileContentPreview => 'ファイル内容のプレビュー：';

  @override
  String get ok => 'OK';

  @override
  String get openFileLocation => 'ファイルの場所を開く';

  @override
  String get sharedFilterFile => '共有フィルターファイル';

  @override
  String get sharedFile => '共有ファイル';

  @override
  String get cannotOpenFileOrItsDirectory => 'ファイルまたはそのディレクトリを開けません';

  @override
  String get errorOpeningFileLocation => 'ファイル場所を開く際にエラーが発生しました：';

  @override
  String get errorSharingFile => 'ファイルの共有中にエラーが発生しました';

  @override
  String get cannotParseSelectedFile => '選択されたファイルを解析できません。有効なCSV形式で、周波数と応答データが含まれていることを確認してください。';

  @override
  String get cannotExtractValidFrequencyAndResponseDataFromCSV => 'CSVから有効な周波数と応答データを抽出できません。';

  @override
  String get importSuccess => 'インポートに成功しました';

  @override
  String get successfullyImportedFile => 'ファイルのインポートに成功しました';

  @override
  String get errorImportingFile => 'ファイルのインポート中にエラーが発生しました';

  @override
  String get noDataToExport => 'エクスポートするデータがありません。';

  @override
  String get fileSavedToAppFolder => 'ファイルはアプリフォルダに保存されました：';

  @override
  String get errorSavingFile => 'ファイル保存中にエラーが発生しました：';

  @override
  String get saveMergedFilterFile => '結合されたフィルターファイルを保存';

  @override
  String get startUsing => '利用を開始';

  @override
  String get nextPage => '次のページ';

  @override
  String get background => '背景';

  @override
  String get confirmDelete => '削除確認';

  @override
  String get confirmDeleteHint => 'この背景画像を削除してもよろしいですか？';

  @override
  String get opacity => '透明度';

  @override
  String get blur => 'ぼかし';

  @override
  String get selectFromAlbum => 'アルバムから選択';

  @override
  String get takePhoto => '写真を撮る';

  @override
  String get addBackground => '背景を追加';

  @override
  String get deleteBackgroundFailed => '背景の削除に失敗しました';

  @override
  String get saveBackgroundFailed => '背景の保存に失敗しました';

  @override
  String get errorExportingFile => 'ファイルのエクスポート中にエラーが発生しました';

  @override
  String get skip => 'スキップ';

  @override
  String get version => 'バージョン';

  @override
  String get copyright => ' 2025 Topping ';

  @override
  String get newFirmwareAvailable => '新しいファームウェアが利用可能です';

  @override
  String get tip => 'ヒント';

  @override
  String get firmwareUpgrade => 'ファームウェアアップグレード';

  @override
  String get firmwareUpgrading => 'ファームウェアをアップグレード中';

  @override
  String get firmwareUpgradeSuccess => 'ファームウェアのアップグレードに成功しました';

  @override
  String get firmwareUpgradeFailed => 'ファームウェアのアップグレードに失敗しました';

  @override
  String get firmwareUpgradeConfirm => 'ファームウェアをアップグレードしますか？';

  @override
  String get firmwareUpgradeWarning => 'アップグレード中はデバイスを切断しないでください';

  @override
  String get firmwareSize => 'ファームウェアサイズ';

  @override
  String get firmwareDescription => '説明';

  @override
  String get firmwareForceUpdate => '強制アップデート';

  @override
  String get firmwareDownloading => 'ファームウェアをダウンロード中';

  @override
  String get firmwareInstalling => 'ファームウェアをインストール中';

  @override
  String get settingResetFail => 'リセット失敗';

  @override
  String get firmwareUpdateTitle => 'ファームウェアアップデート';

  @override
  String get firmwareUpdateNewFirmwareFound => '新しいファームウェアが見つかりました';

  @override
  String get firmwareUpdateFirmwareName => 'ファームウェア名';

  @override
  String get firmwareUpdateVersion => 'バージョン';

  @override
  String get firmwareUpdateDeviceModel => 'デバイスモデル';

  @override
  String get firmwareUpdateFileSize => 'ファイルサイズ';

  @override
  String get firmwareUpdateDescription => '更新ノート';

  @override
  String get firmwareUpdateMandatoryUpdateNote => '※このバージョンは必須アップデートです';

  @override
  String get firmwareUpdateStatus => 'アップデート状況';

  @override
  String get firmwareUpdateDownloading => 'ダウンロード中';

  @override
  String get firmwareUpdateUpgrading => 'アップグレード中';

  @override
  String get firmwareUpdateDoNotDisconnect => '接続を切断したり、デバイスの電源を切ったりしないでください';

  @override
  String get firmwareUpdateReadyToUpdate => 'アップデート準備完了';

  @override
  String get firmwareUpdateCancel => 'アップデートをキャンセル';

  @override
  String get firmwareUpdateStart => 'アップデートを開始';

  @override
  String get firmwareUpdateLatestVersion => '既に最新バージョンです';

  @override
  String get firmwareUpdateNoNeed => 'ファームウェアアップデートは不要です';

  @override
  String get firmwareUpdateBack => '戻る';

  @override
  String get firmwareUpdateSuccessTitle => 'アップデート成功';

  @override
  String get firmwareUpdateSuccessMessage => 'デバイスのファームウェアが最新バージョンに更新されました';

  @override
  String get firmwareUpdateSuccessRebootMessage => 'アップグレード成功、デバイスが再起動しています...';

  @override
  String get firmwareUpdateDownloadingTitle => 'ダウンロード中';

  @override
  String get firmwareUpdateDownloadingMessage => 'ファームウェアファイルをダウンロード中...';

  @override
  String get firmwareUpdateErrorTitle => 'エラー';

  @override
  String get firmwareUpdateDownloadFailed => 'ファームウェアのダウンロードに失敗しました';

  @override
  String get firmwareUpdateUpgradingTitle => 'アップグレード中';

  @override
  String get firmwareUpdateUpgradingMessage => 'ファームウェアのアップグレードを開始しています...';

  @override
  String get firmwareUpdateSetDeviceFailed => 'デバイスの設定に失敗しました';

  @override
  String firmwareUpdateErrorDuringUpdate(String error) {
    return 'アップグレード中にエラーが発生しました: $error';
  }

  @override
  String get firmwareUpdateCancelledTitle => 'キャンセル済み';

  @override
  String get firmwareUpdateCancelledMessage => 'ファームウェアのアップグレードがキャンセルされました';

  @override
  String get commonConfirm => '確認';

  @override
  String get checkFirmwareUpdate => 'ファームウェア更新を確認';

  @override
  String get deviceRebootTitle => 'デバイスの再起動';

  @override
  String get deviceRebootMessage => 'デバイスが再起動しています、しばらくお待ちください...';

  @override
  String get infoTitle => '情報';

  @override
  String errorOpeningFile(String error) {
    return 'ファイルを開く際のエラー: $error';
  }

  @override
  String get fileExported => 'ファイルがエクスポートされました';

  @override
  String get shareFile => 'ファイルを共有';

  @override
  String eqWavePainterStarted(String size) {
    return 'EQ Wave Painter started. Size: $size';
  }

  @override
  String targetRawDataPrepared(int count) {
    return 'Target raw data prepared (interpolated): $count points';
  }

  @override
  String sourceFRRawDataPrepared(int count) {
    return 'Source FR raw data prepared (interpolated): $count points';
  }

  @override
  String combinedRawDataPrepared(int count) {
    return 'Combined raw data prepared: $count points';
  }

  @override
  String individualBandsRawDataPrepared(int bandCount, int pointCount) {
    return 'Individual bands raw data prepared: $bandCount bands, total points: $pointCount';
  }

  @override
  String filteredFRRawDataPrepared(int count) {
    return 'Filtered FR raw data prepared (based on interpolated source): $count points';
  }

  @override
  String dynamicDbRangeCalculated(String min, String max) {
    return 'Dynamic dB range calculated: Min=$min, Max=$max';
  }

  @override
  String get coordinateConverterCreated => 'Coordinate converter created and chart height adjusted';

  @override
  String get gridAndLabelsDrawn => 'Grid and labels drawn';

  @override
  String get drawingTargetCurve => 'Drawing Target Curve...';

  @override
  String get drawingSourceFRCurve => 'Drawing Source FR Curve...';

  @override
  String get drawingIndividualBands => 'Drawing Individual Bands...';

  @override
  String get drawingCombinedCurve => 'Drawing Combined Curve...';

  @override
  String get drawingFilteredFRCurve => 'Drawing Filtered FR Curve...';

  @override
  String get drawingFlatLine => 'Drawing Flat Line...';

  @override
  String get eqWavePainterFinished => 'EQ Wave Painter finished';

  @override
  String finalDynamicDbRange(String min, String max) {
    return 'Final Dynamic dB range: Min=$min, Max=$max';
  }

  @override
  String get peqSettings => 'PEQ設定';

  @override
  String get importExport => 'インポートとエクスポート';

  @override
  String get configManagement => '設定管理';

  @override
  String get addConfig => '設定追加';

  @override
  String get configName => '設定名';

  @override
  String get description => '説明（オプション）';

  @override
  String get configNameHint => '例：ベースブースト、クリアボーカルなど';

  @override
  String get configDescriptionHint => 'この設定の目的を簡単に説明してください';

  @override
  String get add => '追加';

  @override
  String get addNewConfig => '新しい設定を追加';

  @override
  String get importTarget => 'ターゲットをインポート';

  @override
  String get importSourceFR => 'ソースFRをインポート';

  @override
  String get exportCombinedFilter => '結合フィルターをエクスポート';

  @override
  String get targetFR => 'ターゲットFR';

  @override
  String get editConfig => '設定編集';

  @override
  String get deleteConfig => '設定削除';

  @override
  String deleteConfigConfirmation(Object name) {
    return '設定\"$name\"を削除してもよろしいですか？この操作は元に戻せません。';
  }

  @override
  String get deleteTargetFile => 'ターゲットファイルを削除';

  @override
  String get deleteSourceFRFile => 'ソースFRファイルを削除';

  @override
  String deleteFileConfirmation(Object name) {
    return 'ファイル\"$name\"を削除してもよろしいですか？この操作は元に戻せません。';
  }

  @override
  String get noConfigsMessage => 'まだ設定がありません。\"+\"をクリックして新しい設定を作成してください。';

  @override
  String get devicePowerOff => 'デバイスの電源が切れています';

  @override
  String get devicePowerOffHint => 'デバイスを操作するには電源を入れてください';

  @override
  String get powerOn => '電源オン';

  @override
  String get inputOptical1 => '光デジタル1';

  @override
  String get inputOptical2 => '光デジタル2';

  @override
  String get inputCoaxial1 => '同軸デジタル1';

  @override
  String get inputCoaxial2 => '同軸デジタル2';

  @override
  String get inputAes => 'AES';

  @override
  String get inputIis => 'IIS';

  @override
  String get outputDac => 'DAC';

  @override
  String get outputPreamp => 'プリアンプ';

  @override
  String get outputAll => '全て';

  @override
  String get auto => '自動';

  @override
  String get enabled => '有効';

  @override
  String get standard => '標準';

  @override
  String get inverted => '反転';

  @override
  String get swapped => '交換';

  @override
  String get displaySignal => '信号';

  @override
  String get display12V => '12V';

  @override
  String get displayOff => 'オフ';

  @override
  String get usbTypeC => 'Type-C';

  @override
  String get usbTypeB => 'Type-B';

  @override
  String get powerTriggerOff => 'オフ';

  @override
  String get powerTrigger12V => '12V';

  @override
  String get multiFunctionKeyOutputSelect => '出力選択';

  @override
  String get multiFunctionKeyScreenOff => '画面オフ';

  @override
  String get usbSelect => 'USB選択';

  @override
  String get usbDsdPassthrough => 'USB DSDパススルー';

  @override
  String get iisPhase => 'IISフェーズ';

  @override
  String get iisDsdChannel => 'IIS DSDチャンネル';
}
