// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Korean (`ko`).
class AppLocalizationsKo extends AppLocalizations {
  AppLocalizationsKo([String locale = 'ko']) : super(locale);

  @override
  String get appName => '토핀 홈';

  @override
  String get homeTitle => '내 제품';

  @override
  String get homeAddHint => '제품을 추가하여 더 많은 기능을 이용해보세요';

  @override
  String get addDevice => '기기 추가';

  @override
  String get noDevice => '기기 없음';

  @override
  String get addDeviceHint => '제품을 추가하여 더 나은 사용 경험을 얻으세요';

  @override
  String get foundDevice => '기기 발견';

  @override
  String get devices => '기기';

  @override
  String get loginClick => '로그인하기';

  @override
  String get about => '소개';

  @override
  String get quickStart => '빠른 시작';

  @override
  String get feedback => '피드백';

  @override
  String get customBackground => '배경 사용자 정의';

  @override
  String get logout => '로그아웃';

  @override
  String get registerTitle => '회원가입';

  @override
  String get phone => '핸드폰 번호';

  @override
  String get phoneHint => '핸드폰 번호를 입력하세요';

  @override
  String get verifyCode => '인증 코드';

  @override
  String get getSmsVerifyCode => 'SMS 인증 코드 받기';

  @override
  String get agreement => '이용약관 및 개인정보처리방침에 동의합니다';

  @override
  String get agreementLink => '《이용약관》';

  @override
  String get agree => '동의';

  @override
  String get privacyPolicy => '《개인정보처리방침》';

  @override
  String get register => '회원가입';

  @override
  String get login => '로그인';

  @override
  String get loginTitle => '로그인';

  @override
  String get loginPhoneHint => '핸드폰 번호를 입력하세요';

  @override
  String get loginVerifyCode => '인증 코드';

  @override
  String get loginGetSmsVerifyCode => 'SMS 인증 코드 받기';

  @override
  String get loginPassword => '비밀번호로 로그인';

  @override
  String get loginForgetPassword => '비밀번호 찾기';

  @override
  String get loginFail => '로그인 실패';

  @override
  String get passwordError => '비밀번호 오류';

  @override
  String get phoneNotRegistered => '등록되지 않은 핸드폰 번호';

  @override
  String get noAccount => '계정이 없으신가요?';

  @override
  String get registerNow => '지금 가입하기';

  @override
  String get tips => '알림';

  @override
  String get graphicCodeHint => '그래픽 인증 코드를 입력하세요';

  @override
  String get agreeToTermsHint => '이용약관 및 개인정보처리방침에 동의해주세요';

  @override
  String get verificationCodeHint => '인증 코드를 입력하세요';

  @override
  String get usernameHint => '사용자 이름을 입력하세요';

  @override
  String get passwordHint => '비밀번호를 입력하세요';

  @override
  String get confirmPasswordHint => '비밀번호를 다시 입력하세요';

  @override
  String get passwordMismatch => '비밀번호가 일치하지 않습니다';

  @override
  String get registerSuccess => '회원가입 성공';

  @override
  String get registerSuccessHint => '핸드폰 번호와 비밀번호로 로그인하세요';

  @override
  String get registerFailed => '회원가입 실패';

  @override
  String get getVerificationCode => 'SMS 인증 코드 받기';

  @override
  String get verificationCodeSent => '인증 코드가 다음으로 전송되었습니다';

  @override
  String get next => '다음';

  @override
  String get secondResend => '초 후 재전송';

  @override
  String get settingAccount => '계정 설정';

  @override
  String get registerComplete => '회원가입 완료';

  @override
  String get username => '사용자 이름';

  @override
  String get password => '비밀번호';

  @override
  String get confirmPassword => '비밀번호 확인';

  @override
  String get inputError => '입력 오류';

  @override
  String get inputCannotBeEmpty => '입력란을 비워둘 수 없습니다';

  @override
  String get invalidCaptcha => '인증 코드 오류';

  @override
  String get forgetPassword => '비밀번호 찾기';

  @override
  String get forgetPasswordTitle => '비밀번호 찾기';

  @override
  String get forgetPasswordPhoneTitle => '핸드폰 번호를 입력하세요';

  @override
  String get forgetPasswordPhoneHint => '등록된 핸드폰 번호로 인증 코드를 받아 다음 단계를 진행하세요';

  @override
  String get forgetPasswordVerifyCode => '인증 코드';

  @override
  String get forgetPasswordGetSmsVerifyCode => 'SMS 인증 코드 받기';

  @override
  String get forgetPasswordNext => '다음';

  @override
  String get forgetPasswordNewPassword => '새 비밀번호';

  @override
  String get forgetPasswordNewPasswordHint => '새 비밀번호를 입력하세요';

  @override
  String get forgetPasswordConfirmPassword => '비밀번호 확인';

  @override
  String get forgetPasswordConfirmPasswordHint => '새 비밀번호를 다시 입력하세요';

  @override
  String get forgetPasswordReset => '비밀번호 재설정';

  @override
  String get forgetPasswordSuccess => '비밀번호 재설정 성공';

  @override
  String get forgetPasswordSuccessHint => '새 비밀번호로 로그인하세요';

  @override
  String get forgetPasswordSuccessBack => '로그인으로 돌아가기';

  @override
  String get forgetPasswordSuccessBackHome => '홈으로 돌아가기';

  @override
  String get forgetPasswordSuccessBackLogin => '로그인으로 돌아가기';

  @override
  String get forgetPasswordSuccessBackRegister => '회원가입으로 돌아가기';

  @override
  String get forgetPasswordSuccessBackForgetPassword => '비밀번호 찾기로 돌아가기';

  @override
  String get aboutTitle => '소개';

  @override
  String get aboutVersion => '버전';

  @override
  String get aboutVersionHint => '1.0.0';

  @override
  String get aboutUpdate => '업데이트 확인';

  @override
  String get aboutUpdateHint => '최신 버전입니다';

  @override
  String get aboutUpdateSuccess => '업데이트 성공';

  @override
  String get feedbackTitle => '피드백';

  @override
  String get feedbackHint => '귀중한 의견을 남겨주시면 제품을 지속적으로 개선하겠습니다';

  @override
  String get feedbackContact => '연락처';

  @override
  String get feedbackContactHint => '연락처를 입력하세요';

  @override
  String get feedbackContentHint => '피드백 내용을 입력하세요';

  @override
  String get feedbackImage => '이미지 업로드';

  @override
  String get feedbackImageHint => '최대 5장까지 업로드 가능합니다';

  @override
  String get feedbackDevice => '기기 모델';

  @override
  String get feedbackDeviceHint => '기기 모델을 선택하세요';

  @override
  String get feedbackSubmit => '제출';

  @override
  String get feedbackSuccess => '제출 성공';

  @override
  String get feedbackSuccessHint => '피드백 감사합니다. 빠른 시일 내에 처리하겠습니다';

  @override
  String get feedbackType => '피드백 유형';

  @override
  String get feedbackTypeRequired => '피드백 유형을 선택하세요';

  @override
  String get feedbackError => '제출 실패, 나중에 다시 시도하세요';

  @override
  String get error => '오류';

  @override
  String get logoutTitle => '로그아웃';

  @override
  String get logoutHint => '로그아웃하시겠습니까?';

  @override
  String get cancel => '취소';

  @override
  String get confirm => '확인';

  @override
  String get deviceNotFoundTitle => '기기를 찾을 수 없습니까?';

  @override
  String get deviceNotFoundHint => '1. 기기가 켜져 있는지 확인하세요\n2. 기기가 휴대폰과 블루투스로 연결되어 있는지 확인하세요\n3. 위 단계를 완료하고 새로고침 버튼을 눌러 다시 검색하세요\n4. 일부 휴대폰 시스템에서는 블루투스 BLE 연결을 위해 위치 서비스를 활성화해야 할 수 있습니다. 위치 서비스를 활성화하고 다시 새로고침을 시도해보세요';

  @override
  String get operateDescription => '작동 설명';

  @override
  String get blueHeadAmp => '블루투스 헤드폰 앰프';

  @override
  String get decoderAmp => '디코더 앰프';

  @override
  String get headset => '헤드폰';

  @override
  String get bluetoothHeadphoneAmplifier => '블루투스 및 헤드폰';

  @override
  String get player => '플레이어';

  @override
  String get inputLinker => 'Linker 컨트롤 IP 수동 입력';

  @override
  String get connect => '연결됨';

  @override
  String get disconnected => '연결 해제됨';

  @override
  String get connecting => '연결 중';

  @override
  String get disconnecting => '연결 해제 중';

  @override
  String get disconnect => '연결되지 않음';

  @override
  String get personalCenter => '개인 센터';

  @override
  String get editPersonalInfo => '개인 정보 편집';

  @override
  String get accountSecurity => '계정 보안 설정';

  @override
  String get avatar => '프로필 사진';

  @override
  String get gender => '성별';

  @override
  String get secret => '비공개';

  @override
  String get male => '남성';

  @override
  String get female => '여성';

  @override
  String get birthday => '생일';

  @override
  String get signature => '상태 메시지';

  @override
  String get region => '지역';

  @override
  String get nickname => '닉네임';

  @override
  String get save => '저장';

  @override
  String get success => '성공';

  @override
  String get updateSuccess => '업데이트 성공';

  @override
  String get userNotFound => '사용자를 찾을 수 없습니다';

  @override
  String get loginPasswordModify => '로그인 비밀번호 변경';

  @override
  String get accountAndBinding => '계정 및 연동 설정';

  @override
  String get modifyPhone => '핸드폰 번호 변경';

  @override
  String get cancelAccount => '계정 삭제';

  @override
  String get modifyPassword => '비밀번호 변경';

  @override
  String get oldPassword => '기존 비밀번호';

  @override
  String get newPassword => '새 비밀번호';

  @override
  String get confirmNewPassword => '새 비밀번호 확인';

  @override
  String get modifyPasswordSuccess => '비밀번호 변경 성공';

  @override
  String get informationIncomplete => '정보가 불완전합니다';

  @override
  String get oldPasswordError => '기존 비밀번호 오류';

  @override
  String get newPasswordAndConfirmPasswordNotConsistent => '새 비밀번호와 확인 비밀번호가 일치하지 않습니다';

  @override
  String get oldPasswordAndNewPasswordCannotBeTheSame => '기존 비밀번호와 새 비밀번호는 같을 수 없습니다';

  @override
  String get newPasswordCannotBeTheSameAsTheOldPassword => '새 비밀번호는 기존 비밀번호와 같을 수 없습니다';

  @override
  String get emailBinding => '이메일 연동';

  @override
  String get notBound => '연동되지 않음';

  @override
  String get bound => '연동됨';

  @override
  String get nowEmailVerification => '이메일 인증하기';

  @override
  String get emailVerificationPrompt => '연동하려는 이메일을 입력하세요. 이후 이메일 변경 및 비밀번호 찾기에 사용할 수 있습니다';

  @override
  String get email => '이메일';

  @override
  String get sendVerificationCode => '인증 코드 전송';

  @override
  String get smsVerificationCode => 'SMS 인증 코드';

  @override
  String get verificationCodeHasBeenSent => '인증 코드가 전송되었습니다';

  @override
  String get accountBindingSuccess => '계정 연동 성공';

  @override
  String get emailEmpty => '이메일을 입력하세요';

  @override
  String get invalidEmail => '이메일 형식이 올바르지 않습니다';

  @override
  String get emailBindSuccess => '이메일 연동 성공';

  @override
  String get unbindEmail => '이메일 연동 해제';

  @override
  String get unbindEmailConfirm => '이메일 연동을 해제하시겠습니까?';

  @override
  String get emailUnbindSuccess => '이메일 연동 해제 성공';

  @override
  String get boundEmail => '연동된 이메일';

  @override
  String get unbind => '연동 해제';

  @override
  String get bind => '연동하기';

  @override
  String get getEmailVerificationCode => '이메일 인증 코드 받기';

  @override
  String get bindNewPhone => '새 핸드폰 번호 연동';

  @override
  String get bindNewPhonePrompt => '새 핸드폰 번호를 입력하고 인증 코드를 받아 확인하세요';

  @override
  String get bindNewPhoneSuccess => '새 핸드폰 번호 연동 성공';

  @override
  String get bindNewPhoneFailure => '새 핸드폰 번호 연동 실패';

  @override
  String get bindNewPhoneFailurePrompt => '새 핸드폰 번호 연동 실패, 나중에 다시 시도하세요';

  @override
  String get will => '을(를)';

  @override
  String get accountWillBeCancelled => '연동된 계정 삭제';

  @override
  String get cancellationInstructions => '주의: 계정 삭제는 본인만 할 수 있으며, 삭제 후에는 복구할 수 없습니다. 신중하게 결정하세요.\n계정 삭제 후에는 이 계정을 더 이상 사용할 수 없으며, 계정과 관련된 모든 정보를 복구할 수 없습니다. 다음 항목이 포함됩니다:\n\n1. 이 계정의 개인 정보(프로필 사진, 닉네임, 연결된 제품 등)\n2. 이 계정의 다양한 권한(MQA 멤버십, 포럼 포인트 등)\n\n3. 이 계정의 다양한 콘텐츠(재생 목록 등)\n\n4. 계정 삭제로 인해 발생할 수 있는 기타 결과';

  @override
  String get cancellation => '삭제';

  @override
  String get confirmCancellation => '계정을 삭제하시겠습니까?';

  @override
  String get cancellationSuccess => '삭제 성공';

  @override
  String get cancellationFailure => '삭제 실패';

  @override
  String get exitApp => '앱 종료';

  @override
  String get exitAppPrompt => '앱을 종료하시겠습니까?';

  @override
  String get phoneUpdateSuccess => '핸드폰 번호 변경 성공';

  @override
  String get phoneEmpty => '핸드폰 번호를 입력하세요';

  @override
  String get verificationCodeEmpty => '인증 코드를 입력하세요';

  @override
  String get samePhoneNumber => '새 핸드폰 번호가 기존 번호와 동일합니다';

  @override
  String get verifyOldPhone => '기존 핸드폰 번호 인증';

  @override
  String get setNewPhone => '새 핸드폰 번호 설정';

  @override
  String get oldPhone => '기존 핸드폰 번호';

  @override
  String get newPhone => '새 핸드폰 번호';

  @override
  String get passwordTooShort => '비밀번호는 6자 이상이어야 합니다';

  @override
  String get passwordEmpty => '비밀번호를 입력하세요';

  @override
  String get passwordUpdateSuccess => '비밀번호 업데이트 성공';

  @override
  String get passwordUpdateSuccessRelogin => '비밀번호 변경 성공, 다시 로그인하세요';

  @override
  String get scanning => '스캔 중';

  @override
  String get scanningDevicesHint => '근처의 블루투스 기기를 검색하고 있습니다...';

  @override
  String get startScanningHint => '스캔 시작';

  @override
  String get manualInputIP => 'IP 수동 입력';

  @override
  String get manualInputIPHint => 'Linker 컨트롤 IP를 입력하세요';

  @override
  String get bluetoothAndDac => '블루투스 및 DAC';

  @override
  String get streamer => '스트리머';

  @override
  String get pleaseInputIP => 'IP를 입력하세요';

  @override
  String get deviceNotFoundHint1 => '1. 기기가 켜져 있는지 확인하세요.';

  @override
  String get deviceNotFoundHint2 => '2. 기기가 휴대폰과 블루투스로 연결되어 있는지 확인하세요.';

  @override
  String get deviceNotFoundHint3 => '3. 위 단계를 완료하고 새로고침 버튼을 눌러 다시 검색하세요.';

  @override
  String get deviceNotFoundHint4 => '4. 일부 휴대폰 시스템에서는 블루투스 BLE 연결을 위해 위치 서비스를 활성화해야 할 수 있습니다. 위치 서비스를 활성화하고 다시 새로고침을 시도해보세요.';

  @override
  String get invalidQRCode => '유효하지 않은 QR 코드';

  @override
  String get scanQRCode => 'QR 코드 스캔';

  @override
  String get scanQRCodeHint => '기기의 QR 코드를 스캔하세요';

  @override
  String get scanQRCodeBottomHint => 'QR 코드를 프레임 안에 놓으면 자동으로 스캔됩니다';

  @override
  String get noDevicesFound => '기기를 찾을 수 없습니다';

  @override
  String get discoveredDevices => '연결 가능한 제품';

  @override
  String get discoveredDevicesHint => '여러 개의 연결 가능한 제품이 발견되었습니다';

  @override
  String get tapToConnect => '탭하여 연결';

  @override
  String get availableDevices => '사용 가능한 기기';

  @override
  String get languageCode => 'ko';

  @override
  String get deviceNotFound => '기기를 찾을 수 없습니다';

  @override
  String get connected => '연결됨';

  @override
  String get battery => '배터리';

  @override
  String get tryAgain => '다시 시도';

  @override
  String get volumeControl => '볼륨 조절';

  @override
  String get unlock => '잠금 해제';

  @override
  String get lock => '잠금';

  @override
  String get audioSettings => '오디오 설정';

  @override
  String get displayView => '디스플레이 보기';

  @override
  String get low => '낮음';

  @override
  String get middle => '중간';

  @override
  String get high => '높음';

  @override
  String get equalizer => '이퀄라이저';

  @override
  String get eqPreset => 'EQ 프리셋';

  @override
  String get digitalFilter => '디지털 필터';

  @override
  String get channelBalance => '채널 밸런스';

  @override
  String get dsdMode => 'DSD 모드';

  @override
  String get systemSettings => '시스템 설정';

  @override
  String get displayBrightness => '디스플레이 밝기';

  @override
  String get displayTimeout => '디스플레이 타임아웃';

  @override
  String get autoPowerOff => '자동 전원 끄기';

  @override
  String get disabled => '비활성화';

  @override
  String get ledIndicator => 'LED 표시등';

  @override
  String get connectToAccessSettings => '설정에 접근하기 위해 연결';

  @override
  String get connectionInfo => '연결 정보';

  @override
  String get codec => '코덱';

  @override
  String get deviceInfo => '기기 정보';

  @override
  String get firmwareVersion => '펌웨어 버전';

  @override
  String get serialNumber => '시리얼 번호';

  @override
  String get totalPlayTime => '총 재생 시간';

  @override
  String get deleteDevice => '기기 삭제';

  @override
  String get deleteDeviceConfirm => '기기를 삭제하시겠습니까?';

  @override
  String get delete => '삭제';

  @override
  String get preamplifier => '프리앰프';

  @override
  String get decodeModeDac => 'DAC';

  @override
  String get displayNormal => '일반';

  @override
  String get displayVu => 'VU 미터';

  @override
  String get displayFft => '스펙트럼';

  @override
  String get inputUsb => 'USB';

  @override
  String get inputOptical => '광학';

  @override
  String get inputCoaxial => '동축';

  @override
  String get inputBluetooth => '블루투스';

  @override
  String get languageZh => '중국어';

  @override
  String get languageEn => '영어';

  @override
  String get headphoneGainHigh => '고이득';

  @override
  String get headphoneGainLow => '저이득';

  @override
  String get multiFunctionKeyInputSelect => '입력 선택';

  @override
  String get multiFunctionKeyLineOutSelect => '라인 출력 선택';

  @override
  String get multiFunctionKeyHeadphoneOutSelect => '헤드폰 출력 선택';

  @override
  String get multiFunctionKeyHomeSelect => '홈 선택';

  @override
  String get multiFunctionKeyBrightnessSelect => '밝기 선택';

  @override
  String get multiFunctionKeySleep => '화면 끄기';

  @override
  String get multiFunctionKeyPcmFilterSelect => 'PCM 필터 선택';

  @override
  String get multiFunctionKeyMute => '음소거';

  @override
  String get multiFunctionKeyPeqSelect => 'PEQ 선택';

  @override
  String get outputClose => '닫기';

  @override
  String get outputSingleEnded => 'RCA';

  @override
  String get outputBalanced => 'XLR';

  @override
  String get outputSingleEndedAndBalanced => 'RCA+XLR';

  @override
  String get powerTriggerSignal => '신호';

  @override
  String get powerTriggerVoltage => '12V';

  @override
  String get powerTriggerClose => '닫기';

  @override
  String get screenBrightnessHigh => '높음';

  @override
  String get screenBrightnessMedium => '중간';

  @override
  String get screenBrightnessLow => '낮음';

  @override
  String get screenBrightnessAuto => '자동';

  @override
  String get themeAurora => '오로라';

  @override
  String get themeOrange => '오렌지';

  @override
  String get themePeru => '페루';

  @override
  String get themeGreen => '녹색';

  @override
  String get themeKhaki => '카키';

  @override
  String get themeRose => '로즈';

  @override
  String get themeBlue => '블루';

  @override
  String get themePurple => '퍼플';

  @override
  String get themeWhite => '화이트';

  @override
  String get darkMode => '다크 모드';

  @override
  String get lightMode => '라이트 모드';

  @override
  String get darkModeDescription => '현재 다크 테마 사용 중';

  @override
  String get lightModeDescription => '현재 라이트 테마 사용 중';

  @override
  String get usbTypeUac2 => 'UAC 2.0';

  @override
  String get usbTypeUac1 => 'UAC 1.0';

  @override
  String get deviceTypeBluetooth => '블루투스';

  @override
  String get deviceTypeDac => 'DAC';

  @override
  String get deviceTypeHeadphone => '헤드폰 앰프';

  @override
  String get deviceTypePlayer => '플레이어';

  @override
  String get name => '이름';

  @override
  String get volume => '볼륨';

  @override
  String get headphoneOutput => '헤드폰 출력';

  @override
  String get headphoneGain => '헤드폰 이득';

  @override
  String get inputSelect => '입력 선택';

  @override
  String get outputSelect => '출력 선택';

  @override
  String get advanced => '고급';

  @override
  String get advancedSettings => '고급 설정';

  @override
  String get editName => '이름 편집';

  @override
  String get enterNewName => '새 이름을 입력하세요';

  @override
  String get setting => '설정';

  @override
  String get peq => '이퀄라이저';

  @override
  String get guide => '사용 안내';

  @override
  String get theme => '테마';

  @override
  String get powerTrigger => '전원 트리거';

  @override
  String get audioBalance => '오디오 밸런스';

  @override
  String get filter => '필터';

  @override
  String get decodeMode => '디코드 모드';

  @override
  String get audioMonitoring => '오디오 블루투스';

  @override
  String get bluetoothAptx => '블루투스 APTX';

  @override
  String get relay => '리모컨';

  @override
  String get multiFunctionKey => '다기능 키 사용자 정의';

  @override
  String get usbMode => 'USB';

  @override
  String get screenBrightness => '화면 밝기';

  @override
  String get language => '언어';

  @override
  String get resetSettings => '고급 설정 초기화';

  @override
  String get restoreFactorySettings => '공장 설정 복원';

  @override
  String get factoryReset => 'Factory Reset';

  @override
  String get factoryResetConfirm => 'Are you sure you want to restore the factory settings?';

  @override
  String get channel => '채널';

  @override
  String get resetSettingsConfirmation => '고급 설정을 초기화하시겠습니까?';

  @override
  String get restoreFactorySettingsConfirmation => '공장 설정으로 복원하시겠습니까?';

  @override
  String get displaySettings => 'Display Settings';

  @override
  String get inputSettings => 'Input Settings';

  @override
  String get outputSettings => 'Output Settings';

  @override
  String get functions => 'Functions';

  @override
  String get import => '가져오기';

  @override
  String get export => '내보내기';

  @override
  String get target => '목표';

  @override
  String get sourceFR => '소스 FR';

  @override
  String get eachFilter => '각 필터';

  @override
  String get combinedFilter => '결합 필터';

  @override
  String get filteredFR => '필터링된 주파수 응답';

  @override
  String get raw => '원본';

  @override
  String get compensated => '보정됨';

  @override
  String get preAmplification => '프리앰프';

  @override
  String get gain => '게인';

  @override
  String get peakingFilter => '피킹 필터';

  @override
  String get lowPassFilter => '로우패스 필터';

  @override
  String get highPassFilter => '하이패스 필터';

  @override
  String get lowShelfFilter => '로우쉘프 필터';

  @override
  String get highShelfFilter => '하이쉘프 필터';

  @override
  String get centerFrequency => '중심 주파수';

  @override
  String get connerFrequency => '코너 주파수';

  @override
  String get q => 'Q 값';

  @override
  String get frequency => '주파수';

  @override
  String get type => '유형';

  @override
  String get addFilter => '필터 추가';

  @override
  String get mode => '모드';

  @override
  String get import_target => '목표 곡선 가져오기';

  @override
  String get import_sourceFR => '원본 주파수 응답 곡선 가져오기';

  @override
  String get selectDataFile => '데이터 파일 선택';

  @override
  String get feedbackContent => '피드백 내용';

  @override
  String get problemType => '문제 유형';

  @override
  String get selectDeviceType => '디바이스 유형 선택';

  @override
  String get device => '디바이스';

  @override
  String get addPicture => '사진 추가';

  @override
  String get contact => '연락처';

  @override
  String get feedbackTypeFeatureSuggestion => '기능 제안';

  @override
  String get feedbackTypeBugReport => '버그 보고';

  @override
  String get feedbackTypeUIImprovement => 'UI 개선';

  @override
  String get feedbackTypeOther => '기타';

  @override
  String get checkUpdate => '업데이트 확인';

  @override
  String get checking => '확인 중...';

  @override
  String get alreadyLatestVersion => '최신 버전입니다';

  @override
  String get failed => 'Check update failed';

  @override
  String get foundNewVersion => '새 버전 발견';

  @override
  String get currentVersion => '현재 버전';

  @override
  String get newVersion => '새 버전';

  @override
  String get updateContent => '업데이트 내용';

  @override
  String get later => '나중에';

  @override
  String get updateNow => '지금 업데이트';

  @override
  String get downloading => '다운로드 중...';

  @override
  String get downloadCompleted => '다운로드 완료';

  @override
  String get downloadFailed => '다운로드 실패';

  @override
  String get storagePermissionDenied => '저장소 권한 거부';

  @override
  String get cannotGetDownloadPath => '다운로드 경로를 가져올 수 없음';

  @override
  String get checkUpdateFailed => '업데이트 확인 실패';

  @override
  String get installPermissionDenied => '설치 권한 거부';

  @override
  String get failedToLoadContent => '콘텐츠 로드 실패';

  @override
  String get lastUpdated => '마지막 업데이트';

  @override
  String get termsAndConditions => '이용약관';

  @override
  String get agreementDialogContent => '이 앱을 사용하려면';

  @override
  String get userAgreement => '《이용약관》';

  @override
  String get privacyPolicyText => '개인정보처리방침';

  @override
  String get disagree => '동의하지 않음';

  @override
  String get exportSuccess => '내보내기 성공';

  @override
  String get fileSavedTo => '파일 저장 위치:';

  @override
  String get fileContentPreview => '파일 내용 미리보기:';

  @override
  String get ok => '확인';

  @override
  String get openFileLocation => '파일 위치 열기';

  @override
  String get sharedFilterFile => '공유된 필터 파일';

  @override
  String get sharedFile => '공유된 파일';

  @override
  String get cannotOpenFileOrItsDirectory => '파일 또는 해당 디렉토리를 열 수 없습니다';

  @override
  String get errorOpeningFileLocation => '파일 위치를 여는 중 오류 발생:';

  @override
  String get errorSharingFile => '파일 공유 중 오류 발생';

  @override
  String get cannotParseSelectedFile => '선택한 파일을 구문 분석할 수 없습니다. 유효한 CSV 형식이며 주파수 및 응답 데이터가 포함되어 있는지 확인하십시오.';

  @override
  String get cannotExtractValidFrequencyAndResponseDataFromCSV => 'CSV에서 유효한 주파수 및 응답 데이터를 추출할 수 없습니다.';

  @override
  String get importSuccess => '가져오기 성공';

  @override
  String get successfullyImportedFile => '파일을 성공적으로 가져왔습니다';

  @override
  String get errorImportingFile => '파일 가져오기 중 오류 발생';

  @override
  String get noDataToExport => '내보낼 데이터가 없습니다.';

  @override
  String get fileSavedToAppFolder => '파일이 앱 폴더에 저장됨:';

  @override
  String get errorSavingFile => '파일 저장 중 오류 발생:';

  @override
  String get saveMergedFilterFile => '병합된 필터 파일 저장';

  @override
  String get startUsing => '사용 시작';

  @override
  String get nextPage => '다음 페이지';

  @override
  String get background => '배경';

  @override
  String get confirmDelete => '삭제 확인';

  @override
  String get confirmDeleteHint => '이 배경 이미지를 삭제하시겠습니까?';

  @override
  String get opacity => '불투명도';

  @override
  String get blur => '흐림 효과';

  @override
  String get selectFromAlbum => '앨범에서 선택';

  @override
  String get takePhoto => '사진 촬영';

  @override
  String get addBackground => '배경 추가';

  @override
  String get deleteBackgroundFailed => '배경 삭제 실패';

  @override
  String get saveBackgroundFailed => '배경 저장 실패';

  @override
  String get errorExportingFile => '파일 내보내기 중 오류 발생';

  @override
  String get skip => '건너뛰기';

  @override
  String get version => '버전';

  @override
  String get copyright => ' 2025 Topping 테크놀로지 판권소유';

  @override
  String get newFirmwareAvailable => '새 펌웨어 사용 가능';

  @override
  String get tip => '팁';

  @override
  String get firmwareUpgrade => '펌웨어 업그레이드';

  @override
  String get firmwareUpgrading => '펌웨어 업그레이드 중';

  @override
  String get firmwareUpgradeSuccess => '펌웨어 업그레이드 성공';

  @override
  String get firmwareUpgradeFailed => '펌웨어 업그레이드 실패';

  @override
  String get firmwareUpgradeConfirm => '펌웨어를 업그레이드하시겠습니까?';

  @override
  String get firmwareUpgradeWarning => '업그레이드 중에는 장치 연결을 해제하지 마십시오';

  @override
  String get firmwareSize => '펌웨어 크기';

  @override
  String get firmwareDescription => '설명';

  @override
  String get firmwareForceUpdate => '강제 업데이트';

  @override
  String get firmwareDownloading => '펌웨어 다운로드 중';

  @override
  String get firmwareInstalling => '펌웨어 설치 중';

  @override
  String get settingResetFail => '리셋 실패';

  @override
  String get firmwareUpdateTitle => '펌웨어 업데이트';

  @override
  String get firmwareUpdateNewFirmwareFound => '새 펌웨어 발견';

  @override
  String get firmwareUpdateFirmwareName => '펌웨어 이름';

  @override
  String get firmwareUpdateVersion => '버전';

  @override
  String get firmwareUpdateDeviceModel => '기기 모델';

  @override
  String get firmwareUpdateFileSize => '파일 크기';

  @override
  String get firmwareUpdateDescription => '업데이트 노트';

  @override
  String get firmwareUpdateMandatoryUpdateNote => '* 이 버전은 필수 업데이트입니다';

  @override
  String get firmwareUpdateStatus => '업데이트 상태';

  @override
  String get firmwareUpdateDownloading => '다운로드 중';

  @override
  String get firmwareUpdateUpgrading => '업그레이드 중';

  @override
  String get firmwareUpdateDoNotDisconnect => '연결을 끊거나 기기의 전원을 끄지 마십시오';

  @override
  String get firmwareUpdateReadyToUpdate => '업데이트 준비 완료';

  @override
  String get firmwareUpdateCancel => '업데이트 취소';

  @override
  String get firmwareUpdateStart => '업데이트 시작';

  @override
  String get firmwareUpdateLatestVersion => '이미 최신 버전입니다';

  @override
  String get firmwareUpdateNoNeed => '펌웨어 업데이트가 필요하지 않습니다';

  @override
  String get firmwareUpdateBack => '뒤로';

  @override
  String get firmwareUpdateSuccessTitle => '업데이트 성공';

  @override
  String get firmwareUpdateSuccessMessage => '기기 펌웨어가 최신 버전으로 업데이트되었습니다';

  @override
  String get firmwareUpdateSuccessRebootMessage => '업그레이드 성공, 기기가 재부팅 중입니다...';

  @override
  String get firmwareUpdateDownloadingTitle => '다운로드 중';

  @override
  String get firmwareUpdateDownloadingMessage => '펌웨어 파일을 다운로드 중입니다...';

  @override
  String get firmwareUpdateErrorTitle => '오류';

  @override
  String get firmwareUpdateDownloadFailed => '펌웨어 다운로드 실패';

  @override
  String get firmwareUpdateUpgradingTitle => '업그레이드 중';

  @override
  String get firmwareUpdateUpgradingMessage => '펌웨어 업그레이드를 시작 중입니다...';

  @override
  String get firmwareUpdateSetDeviceFailed => '기기 설정 실패';

  @override
  String firmwareUpdateErrorDuringUpdate(String error) {
    return '업그레이드 중 오류 발생: $error';
  }

  @override
  String get firmwareUpdateCancelledTitle => '취소됨';

  @override
  String get firmwareUpdateCancelledMessage => '펌웨어 업그레이드가 취소되었습니다';

  @override
  String get commonConfirm => '확인';

  @override
  String get checkFirmwareUpdate => '펌웨어 업데이트 확인';

  @override
  String get deviceRebootTitle => '기기 재부팅';

  @override
  String get deviceRebootMessage => '기기가 다시 시작 중입니다. 잠시 기다려 주십시오...';

  @override
  String get infoTitle => '정보';

  @override
  String errorOpeningFile(String error) {
    return '파일 열기 오류: $error';
  }

  @override
  String get fileExported => '파일이 내보내기 되었습니다';

  @override
  String get shareFile => '파일 공유';

  @override
  String eqWavePainterStarted(String size) {
    return 'EQ Wave Painter started. Size: $size';
  }

  @override
  String targetRawDataPrepared(int count) {
    return 'Target raw data prepared (interpolated): $count points';
  }

  @override
  String sourceFRRawDataPrepared(int count) {
    return 'Source FR raw data prepared (interpolated): $count points';
  }

  @override
  String combinedRawDataPrepared(int count) {
    return 'Combined raw data prepared: $count points';
  }

  @override
  String individualBandsRawDataPrepared(int bandCount, int pointCount) {
    return 'Individual bands raw data prepared: $bandCount bands, total points: $pointCount';
  }

  @override
  String filteredFRRawDataPrepared(int count) {
    return 'Filtered FR raw data prepared (based on interpolated source): $count points';
  }

  @override
  String dynamicDbRangeCalculated(String min, String max) {
    return 'Dynamic dB range calculated: Min=$min, Max=$max';
  }

  @override
  String get coordinateConverterCreated => 'Coordinate converter created and chart height adjusted';

  @override
  String get gridAndLabelsDrawn => 'Grid and labels drawn';

  @override
  String get drawingTargetCurve => 'Drawing Target Curve...';

  @override
  String get drawingSourceFRCurve => 'Drawing Source FR Curve...';

  @override
  String get drawingIndividualBands => 'Drawing Individual Bands...';

  @override
  String get drawingCombinedCurve => 'Drawing Combined Curve...';

  @override
  String get drawingFilteredFRCurve => 'Drawing Filtered FR Curve...';

  @override
  String get drawingFlatLine => 'Drawing Flat Line...';

  @override
  String get eqWavePainterFinished => 'EQ Wave Painter finished';

  @override
  String finalDynamicDbRange(String min, String max) {
    return 'Final Dynamic dB range: Min=$min, Max=$max';
  }

  @override
  String get peqSettings => 'PEQ 설정';

  @override
  String get importExport => '가져오기 및 내보내기';

  @override
  String get configManagement => '구성 관리';

  @override
  String get addConfig => '구성 추가';

  @override
  String get configName => '구성 이름';

  @override
  String get description => '설명 (선택사항)';

  @override
  String get configNameHint => '예: 베이스 부스트, 보컬 강화 등';

  @override
  String get configDescriptionHint => '이 구성의 용도를 간략히 설명하세요';

  @override
  String get add => '추가';

  @override
  String get addNewConfig => '새 구성 추가';

  @override
  String get importTarget => '대상 가져오기';

  @override
  String get importSourceFR => '소스 FR 가져오기';

  @override
  String get exportCombinedFilter => '결합된 필터 내보내기';

  @override
  String get targetFR => '대상 FR';

  @override
  String get editConfig => '구성 편집';

  @override
  String get deleteConfig => '구성 삭제';

  @override
  String deleteConfigConfirmation(Object name) {
    return '구성 \"$name\"을(를) 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.';
  }

  @override
  String get deleteTargetFile => '대상 파일 삭제';

  @override
  String get deleteSourceFRFile => '소스 FR 파일 삭제';

  @override
  String deleteFileConfirmation(Object name) {
    return '파일 \"$name\"을(를) 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.';
  }

  @override
  String get noConfigsMessage => '아직 구성이 없습니다. \"+\"를 클릭하여 새 구성을 생성하세요.';

  @override
  String get devicePowerOff => '기기가 꺼져 있습니다';

  @override
  String get devicePowerOffHint => '기기를 제어하려면 전원을 켜주세요';

  @override
  String get powerOn => '전원 켜기';

  @override
  String get inputOptical1 => '광학 1';

  @override
  String get inputOptical2 => '광학 2';

  @override
  String get inputCoaxial1 => '동축 1';

  @override
  String get inputCoaxial2 => '동축 2';

  @override
  String get inputAes => 'AES';

  @override
  String get inputIis => 'IIS';

  @override
  String get outputDac => 'DAC';

  @override
  String get outputPreamp => '프리앤프';

  @override
  String get outputAll => '모두';

  @override
  String get auto => '자동';

  @override
  String get enabled => '활성화';

  @override
  String get standard => '표준';

  @override
  String get inverted => '반전';

  @override
  String get swapped => '교체';

  @override
  String get displaySignal => '신호';

  @override
  String get display12V => '12V';

  @override
  String get displayOff => '끄기';

  @override
  String get usbTypeC => 'Type-C';

  @override
  String get usbTypeB => 'Type-B';

  @override
  String get powerTriggerOff => '끄기';

  @override
  String get powerTrigger12V => '12V';

  @override
  String get multiFunctionKeyOutputSelect => '출력 선택';

  @override
  String get multiFunctionKeyScreenOff => '화면 끄기';

  @override
  String get usbSelect => 'USB 선택';

  @override
  String get usbDsdPassthrough => 'USB DSD 패스스루';

  @override
  String get iisPhase => 'IIS 위상';

  @override
  String get iisDsdChannel => 'IIS DSD 채널';
}
