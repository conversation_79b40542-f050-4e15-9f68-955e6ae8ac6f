{"@@locale": "zh", "appName": "拓品之家", "homeTitle": "我的产品", "homeAddHint": "添加产品，带来更多更好的操作体验", "addDevice": "添加设备", "noDevice": "暂无设备", "addDeviceHint": "点击添加新设备", "foundDevice": "发现设备", "devices": "设备", "loginClick": "点击登录", "about": "关于", "quickStart": "快速入门", "feedback": "意见反馈", "customBackground": "自定义背景", "logout": "退出登录", "registerTitle": "注册", "phone": "手机号", "phoneHint": "请输入手机号", "verifyCode": "验证码", "getSmsVerifyCode": "获取短信验证码", "agreement": "我已阅读并同意", "agreementLink": "《用户协议》", "agree": "同意", "privacyPolicy": "《隐私政策》", "register": "注册", "login": "登录", "loginTitle": "登录", "loginPhoneHint": "请输入手机号", "loginVerifyCode": "验证码", "loginGetSmsVerifyCode": "获取短信验证码", "loginPassword": "密码登录", "loginForgetPassword": "忘记密码", "loginFail": "登录失败", "passwordError": "密码错误", "phoneNotRegistered": "手机号未注册", "noAccount": "还没有账号？", "registerNow": "立即注册", "tips": "提示", "graphicCodeHint": "请输入图形验证码", "agreeToTermsHint": "请同意用户协议和隐私政策", "verificationCodeHint": "请输入验证码", "usernameHint": "请输入用户名", "passwordHint": "请输入密码", "confirmPasswordHint": "请再次输入密码", "passwordMismatch": "两次输入密码不一致", "registerSuccess": "注册成功", "registerSuccessHint": "请使用手机号和密码登录", "registerFailed": "注册失败", "getVerificationCode": "获取短信验证码", "verificationCodeSent": "验证码已发送至", "next": "下一步", "secondResend": "s后重新发送", "settingAccount": "账号设置", "registerComplete": "完成注册", "username": "用户名", "password": "密码", "confirmPassword": "确认密码", "inputError": "输入有误", "inputCannotBeEmpty": "输入不能为空", "invalidCaptcha": "验证码错误", "forgetPassword": "忘记密码", "forgetPasswordTitle": "忘记密码", "forgetPasswordPhoneTitle": "请输入手机号", "forgetPasswordPhoneHint": "先通过您已绑定的手机号获取验证码，再进行下一步操作", "forgetPasswordVerifyCode": "验证码", "forgetPasswordGetSmsVerifyCode": "获取短信验证码", "forgetPasswordNext": "下一步", "forgetPasswordNewPassword": "新密码", "forgetPasswordNewPasswordHint": "请输入新密码", "forgetPasswordConfirmPassword": "确认密码", "forgetPasswordConfirmPasswordHint": "请再次输入新密码", "forgetPasswordReset": "重置密码", "forgetPasswordSuccess": "重置密码成功", "forgetPasswordSuccessHint": "请使用新密码登录", "forgetPasswordSuccessBack": "返回登录", "forgetPasswordSuccessBackHome": "返回首页", "forgetPasswordSuccessBackLogin": "返回登录", "forgetPasswordSuccessBackRegister": "返回注册", "forgetPasswordSuccessBackForgetPassword": "返回忘记密码", "aboutTitle": "关于", "aboutVersion": "版本号", "aboutVersionHint": "1.0.0", "aboutUpdate": "检查更新", "aboutUpdateHint": "当前已是最新版本", "aboutUpdateSuccess": "更新成功", "error": "错误", "logoutTitle": "退出登录", "logoutHint": "确定要退出登录吗？", "cancel": "取消", "confirm": "确定", "deviceNotFoundTitle": "未找到您的设备？", "deviceNotFoundHint": "1、请确保设备处于开机状态\n2、请保持设备与手机蓝牙正常连接\n3、完成上述步骤，点击刷新按钮重新搜索。\n4、部分手机系统可能需要开启定位功能后才能进行蓝牙BLE连接，您可以尝试开启定位功能后再次刷新", "operateDescription": "操作说明", "blueHeadAmp": "蓝牙耳放", "decoderAmp": "解码耳放", "headset": "耳机", "bluetoothHeadphoneAmplifier": "蓝牙及耳机", "player": "播放器", "inputLinker": "手动输入Linker控制IP", "connect": "已连接", "disconnected": "已断开", "connecting": "正在连接", "disconnecting": "正在断开", "disconnect": "未连接", "personalCenter": "个人中心", "editPersonalInfo": "编辑个人信息", "accountSecurity": "账号安全设置", "avatar": "头像", "gender": "性别", "secret": "保密", "male": "男", "female": "女", "birthday": "生日", "signature": "个性签名", "region": "地区", "nickname": "昵称", "save": "保存", "success": "成功", "updateSuccess": "更新成功", "userNotFound": "用户不存在", "loginPasswordModify": "登录密码修改", "accountAndBinding": "账号与绑定设置", "modifyPhone": "修改手机号", "cancelAccount": "注销账号", "modifyPassword": "修改密码", "oldPassword": "旧密码", "newPassword": "新密码", "confirmNewPassword": "确认新密码", "modifyPasswordSuccess": "修改密码成功", "informationIncomplete": "信息填写不完整", "oldPasswordError": "旧密码错误", "newPasswordAndConfirmPasswordNotConsistent": "新密码与确认密码不一致", "oldPasswordAndNewPasswordCannotBeTheSame": "旧密码与新密码不能相同", "newPasswordCannotBeTheSameAsTheOldPassword": "新密码不能与旧密码相同", "emailBinding": "邮箱绑定", "notBound": "未绑定", "bound": "已绑定", "nowEmailVerification": "现在邮箱验证", "emailVerificationPrompt": "请输入你需要绑定的邮箱，后续可以进行邮箱改绑和邮箱密码找回", "email": "邮箱", "sendVerificationCode": "发送验证码", "smsVerificationCode": "短信验证码", "verificationCodeHasBeenSent": "验证码已发送", "accountBindingSuccess": "账号绑定成功", "emailEmpty": "邮箱不能为空", "invalidEmail": "邮箱格式不正确", "emailBindSuccess": "邮箱绑定成功", "unbindEmail": "解绑邮箱", "unbindEmailConfirm": "确定要解绑邮箱吗？", "emailUnbindSuccess": "邮箱解绑成功", "boundEmail": "已绑定邮箱", "unbind": "解绑", "bind": "绑定", "getEmailVerificationCode": "获取邮箱验证码", "bindNewPhone": "绑定新手机", "bindNewPhonePrompt": "请输入您的新手机号，点击获取验证码完成验证", "bindNewPhoneSuccess": "绑定新手机成功", "bindNewPhoneFailure": "绑定新手机失败", "bindNewPhoneFailurePrompt": "绑定新手机失败，请稍后重试", "will": "将", "accountWillBeCancelled": "所绑定的账号注销", "cancellationInstructions": "注意：注销账号仅支持账号本人操作，且注销后不支持恢复使用，请您审中操作。\n正式注销后，您将无法再使用本账号，也无法找回账号中以及账号相关的任何内容信息，包括但不限于：\n\n1、该账号下的个人信息（包括但不限于头像、昵称、绑定产品等）\n2、该账号下的各类权益（包括但不限于MQA会员、论坛积分等）\n\n3、该账号下的各类权益（包括但不限于播放列表等）\n\n4、其他应账号注销所（可能）产生的结果", "cancellation": "注销", "confirmCancellation": "确定要注销账号吗？", "cancellationSuccess": "注销成功", "cancellationFailure": "注销失败", "exitApp": "退出应用", "exitAppPrompt": "确定要退出应用吗？", "phoneUpdateSuccess": "手机号修改成功", "phoneEmpty": "手机号不能为空", "verificationCodeEmpty": "验证码不能为空", "samePhoneNumber": "新手机号与原手机号相同", "verifyOldPhone": "验证原手机号", "setNewPhone": "设置新手机号", "oldPhone": "原手机号", "newPhone": "新手机号", "passwordTooShort": "密码长度不能少于6位", "passwordEmpty": "密码不能为空", "passwordUpdateSuccess": "密码修改成功", "passwordUpdateSuccessRelogin": "密码修改成功，请重新登录", "scanning": "正在扫描", "scanningDevicesHint": "正在扫描附近的蓝牙设备...", "startScanningHint": "开始扫描", "manualInputIP": "手动输入IP", "manualInputIPHint": "请输入Linker控制IP", "bluetoothAndDac": "蓝牙及DAC", "streamer": "播放器", "pleaseInputIP": "请输入IP", "deviceNotFoundHint1": "1、请确保设备处于开机状态。", "deviceNotFoundHint2": "2、请保持设备与手机蓝牙正常连接。", "deviceNotFoundHint3": "3、完成上述步骤，点击刷新按钮重新搜索。", "deviceNotFoundHint4": "4、部分手机系统可能需要开启定位功能后才能进行蓝牙BLE连接，您可以尝试开启定位功能后再次刷新。", "invalidQRCode": "无效二维码", "scanQRCode": "扫描二维码", "scanQRCodeHint": "请扫描设备上的二维码", "scanQRCodeBottomHint": "请将二维码放入框内，即可自动扫描", "noDevicesFound": "未找到设备", "discoveredDevices": "可连接产品", "discoveredDevicesHint": "发现多个可连接产品", "tapToConnect": "轻触连接", "availableDevices": "可用设备", "languageCode": "zh", "deviceNotFound": "设备未找到", "connected": "已连接", "battery": "电量", "tryAgain": "再试一次", "volumeControl": "音量控制", "unlock": "解锁", "lock": "锁定", "audioSettings": "音频设置", "displayView": "显示界面", "low": "低", "middle": "中", "high": "高", "equalizer": "均衡器", "eqPreset": "EQ预设", "digitalFilter": "数字滤波器", "channelBalance": "声道平衡", "dsdMode": "DSD模式", "systemSettings": "系统设置", "displayBrightness": "显示亮度", "displayTimeout": "显示超时", "autoPowerOff": "自动关机", "disabled": "禁用", "ledIndicator": "LED指示灯", "connectToAccessSettings": "连接到访问设置", "connectionInfo": "连接信息", "codec": "编解码器", "deviceInfo": "设备信息", "firmwareVersion": "固件版本", "serialNumber": "序列号", "totalPlayTime": "总播放时间", "deleteDevice": "删除设备", "deleteDeviceConfirm": "确定要删除设备吗？", "preamplifier": "前级", "decodeModeDac": "DAC", "displayVu": "VU表", "displayFft": "频谱", "displayNormal": "正常", "inputUsb": "USB", "inputOptical": "光纤", "inputCoaxial": "同轴", "inputBluetooth": "蓝牙", "languageZh": "中文", "languageEn": "英文", "headphoneGainHigh": "高增益", "headphoneGainLow": "低增益", "multiFunctionKeyInputSelect": "输入选择", "multiFunctionKeyLineOutSelect": "线路输出选择", "multiFunctionKeyHeadphoneOutSelect": "耳放输出选择", "multiFunctionKeyHomeSelect": "主页选择", "multiFunctionKeyBrightnessSelect": "亮度选择", "multiFunctionKeySleep": "息屏", "multiFunctionKeyPcmFilterSelect": "PCM滤波器选择", "multiFunctionKeyMute": "静音", "multiFunctionKeyPeqSelect": "PEQ选择", "outputClose": "关闭", "outputSingleEnded": "RCA", "outputBalanced": "XLR", "outputSingleEndedAndBalanced": "RCA+XLR", "powerTriggerSignal": "信号", "powerTriggerVoltage": "12V", "powerTriggerClose": "关闭", "screenBrightnessHigh": "高", "screenBrightnessMedium": "中", "screenBrightnessLow": "低", "screenBrightnessAuto": "自动", "themeAurora": "极光", "themeOrange": "橙色", "themePeru": "秘鲁色", "themeGreen": "豆绿色", "themeKhaki": "深卡其色", "themeRose": "玫瑰棕色", "themeBlue": "蓝色", "themePurple": "幻紫色", "themeWhite": "白色", "darkMode": "深色模式", "lightMode": "浅色模式", "darkModeDescription": "当前使用深色主题", "lightModeDescription": "当前使用浅色主题", "usbTypeUac2": "UAC 2.0", "usbTypeUac1": "UAC 1.0", "deviceTypeBluetooth": "蓝牙", "deviceTypeDac": "DAC", "deviceTypeHeadphone": "耳放", "deviceTypePlayer": "播放器", "name": "名称", "volume": "音量", "headphoneOutput": "耳放输出", "headphoneGain": "耳放增益", "inputSelect": "输入选择", "outputSelect": "输出选择", "advanced": "高级", "advancedSettings": "高级设置", "editName": "修改名称", "enterNewName": "请输入新名称", "setting": "设置", "peq": "均衡器", "guide": "操作指南", "theme": "主题", "powerTrigger": "开关机触发", "audioBalance": "声道平衡", "filter": "滤波器", "decodeMode": "解码模式", "audioMonitoring": "音频蓝牙", "bluetoothAptx": "蓝牙APTX", "relay": "遥控器", "multiFunctionKey": "自定义多功能按键", "usbMode": "USB模式", "screenBrightness": "屏幕亮度", "language": "语言", "resetSettings": "恢复高级设置", "restoreFactorySettings": "恢复出厂设置", "factoryReset": "恢复出厂设置", "factoryResetConfirm": "确定要恢复出厂设置吗？", "channel": "声道", "resetSettingsConfirmation": "确定要恢复高级设置吗？", "restoreFactorySettingsConfirmation": "确定要恢复出厂设置吗？", "displaySettings": "显示设置", "inputSettings": "输入设置", "outputSettings": "输出设置", "functions": "功能", "import": "导入", "export": "导出", "target": "目标", "sourceFR": "源频响", "eachFilter": "每个滤波", "combinedFilter": "合并滤波", "filteredFR": "滤波后频响", "raw": "原始", "compensated": "补偿", "preAmplification": "前级", "peakingFilter": "峰值滤波", "lowPassFilter": "低通滤波", "highPassFilter": "高通滤波", "lowShelfFilter": "低滤搁架", "highShelfFilter": "高滤搁架", "centerFrequency": "中心频率", "connerFrequency": "拐点频率", "type": "类型", "frequency": "频率", "q": "Q值", "gain": "增益", "addFilter": "添加滤波", "mode": "模式", "import_target": "导入目标曲线", "import_sourceFR": "导入源频响曲线", "selectDataFile": "选择数据文件", "feedbackTitle": "意见反馈", "feedbackHint": "请留下您的宝贵意见，我们将不断优化产品", "feedbackContact": "联系方式", "feedbackContactHint": "请输入您的联系方式", "feedbackContent": "反馈内容", "feedbackContentHint": "请输入您的反馈内容", "feedbackImage": "上传图片", "feedbackImageHint": "最多上传5张图片", "feedbackDevice": "设备型号", "feedbackDeviceHint": "请选择您的设备型号", "feedbackSubmit": "提交", "feedbackSuccess": "提交成功", "feedbackSuccessHint": "感谢您的反馈，我们将尽快处理", "feedbackType": "反馈类型", "feedbackTypeRequired": "请选择反馈类型", "feedbackError": "提交失败，请稍后重试", "problemType": "问题类型", "selectDeviceType": "请选择设备类型", "device": "设备", "addPicture": "添加图片", "contact": "联系方式", "feedbackTypeFeatureSuggestion": "功能建议", "feedbackTypeBugReport": "Bug反馈", "feedbackTypeUIImprovement": "UI优化", "feedbackTypeOther": "其他", "checkUpdate": "检查更新", "checking": "检查中...", "alreadyLatestVersion": "已是最新版本", "checkUpdateFailed": "检查更新失败", "foundNewVersion": "发现新版本", "currentVersion": "当前版本", "newVersion": "新版本", "updateContent": "更新内容", "later": "以后再说", "updateNow": "立即更新", "downloading": "下载中...", "downloadCompleted": "下载完成", "downloadFailed": "下载失败", "storagePermissionDenied": "存储权限被拒绝", "cannotGetDownloadPath": "无法获取下载路径", "installPermissionDenied": "安装权限被拒绝", "failedToLoadContent": "加载内容失败", "lastUpdated": "最后更新", "termsAndConditions": "用户协议", "agreementDialogContent": "请您务必审慎阅读、充分理解《用户协议》和《隐私政策》各条款，包括但不限于：为了向您提供产品和服务，我们需要收集您的设备信息、操作日志等个人信息。您可以在设置中查看、变更、删除个人信息并管理您的授权。", "userAgreement": "用户协议", "privacyPolicyText": "隐私政策", "disagree": "不同意", "exportSuccess": "导出成功", "fileSavedTo": "文件已保存至：", "fileContentPreview": "文件内容预览：", "ok": "确定", "openFileLocation": "打开文件位置", "sharedFilterFile": "共享的过滤器文件", "sharedFile": "共享的文件", "cannotOpenFileOrItsDirectory": "无法打开文件或其目录", "errorOpeningFileLocation": "打开文件位置时出错：", "errorSharingFile": "共享文件时出错", "cannotParseSelectedFile": "无法解析选定的文件。请确保它是有效的CSV格式，并包含频率和响应数据。", "cannotExtractValidFrequencyAndResponseDataFromCSV": "无法从CSV中提取有效的频率和响应数据。", "importSuccess": "导入成功", "successfullyImportedFile": "成功导入文件", "errorImportingFile": "导入文件时出错", "noDataToExport": "没有数据可导出。", "fileSavedToAppFolder": "文件已保存到应用文件夹：", "errorSavingFile": "保存文件时出错：", "saveMergedFilterFile": "保存合并的过滤器文件", "startUsing": "开始使用", "nextPage": "下一页", "background": "背景", "confirmDelete": "确认删除", "confirmDeleteHint": "确认要删除此背景图片吗？", "opacity": "不透明度", "blur": "模糊度", "selectFromAlbum": "从相册选择", "takePhoto": "拍照", "addBackground": "添加背景", "deleteBackgroundFailed": "删除背景图片失败", "saveBackgroundFailed": "保存背景图片失败", "connectFailed": "连接失败", "deviceInfoNotFound": "无法找到设备信息，请刷新扫描", "errorExportingFile": "导出文件时出错", "skip": "跳过", "version": "版本", "copyright": " 2025 拓品科技 版权所有", "newFirmwareAvailable": "发现新固件", "tip": "提示", "firmwareUpgrade": "固件升级", "firmwareUpgrading": "正在升级固件", "firmwareUpgradeSuccess": "固件升级成功", "firmwareUpgradeFailed": "固件升级失败", "firmwareUpgradeConfirm": "确定要升级固件吗？", "firmwareUpgradeWarning": "升级过程中请勿断开设备连接", "firmwareSize": "固件大小", "firmwareDescription": "更新说明", "firmwareForceUpdate": "强制更新", "firmwareDownloading": "正在下载固件", "firmwareInstalling": "正在安装固件", "settingResetSuccess": "重置成功", "@settingResetSuccess": {}, "settingResetFail": "重置失败", "@settingResetFail": {}, "firmwareUpdateTitle": "固件升级", "@firmwareUpdateTitle": {"description": "Title for the firmware update page"}, "firmwareUpdateNewFirmwareFound": "发现新固件", "@firmwareUpdateNewFirmwareFound": {}, "firmwareUpdateFirmwareName": "固件名称", "@firmwareUpdateFirmwareName": {}, "firmwareUpdateVersion": "版本号", "@firmwareUpdateVersion": {}, "firmwareUpdateDeviceModel": "设备型号", "@firmwareUpdateDeviceModel": {}, "firmwareUpdateFileSize": "文件大小", "@firmwareUpdateFileSize": {}, "firmwareUpdateDescription": "更新说明", "@firmwareUpdateDescription": {}, "firmwareUpdateMandatoryUpdateNote": "* 此版本为必要更新", "@firmwareUpdateMandatoryUpdateNote": {}, "firmwareUpdateStatus": "升级状态", "@firmwareUpdateStatus": {}, "firmwareUpdateDownloading": "正在下载", "@firmwareUpdateDownloading": {}, "firmwareUpdateUpgrading": "正在升级", "@firmwareUpdateUpgrading": {}, "firmwareUpdateDoNotDisconnect": "请勿断开连接或关闭设备", "@firmwareUpdateDoNotDisconnect": {}, "firmwareUpdateReadyToUpdate": "准备升级", "@firmwareUpdateReadyToUpdate": {}, "firmwareUpdateCancel": "取消升级", "@firmwareUpdateCancel": {}, "firmwareUpdateStart": "开始升级", "@firmwareUpdateStart": {}, "firmwareUpdateLatestVersion": "当前已是最新版本", "@firmwareUpdateLatestVersion": {}, "firmwareUpdateNoNeed": "无需升级固件", "@firmwareUpdateNoNeed": {}, "firmwareUpdateBack": "返回", "@firmwareUpdateBack": {}, "firmwareUpdateSuccessTitle": "升级成功", "@firmwareUpdateSuccessTitle": {}, "firmwareUpdateSuccessMessage": "设备固件已更新至最新版本", "@firmwareUpdateSuccessMessage": {}, "firmwareUpdateSuccessRebootMessage": "升级成功，设备正在重启...", "@firmwareUpdateSuccessRebootMessage": {"description": "Message shown when firmware update succeeds and the device disconnects (likely rebooting)"}, "firmwareUpdateDownloadingTitle": "下载中", "@firmwareUpdateDownloadingTitle": {}, "firmwareUpdateDownloadingMessage": "正在下载固件文件...", "@firmwareUpdateDownloadingMessage": {}, "firmwareUpdateErrorTitle": "错误", "@firmwareUpdateErrorTitle": {}, "firmwareUpdateDownloadFailed": "固件下载失败", "@firmwareUpdateDownloadFailed": {}, "firmwareUpdateUpgradingTitle": "升级中", "@firmwareUpdateUpgradingTitle": {}, "firmwareUpdateUpgradingMessage": "开始升级固件...", "@firmwareUpdateUpgradingMessage": {}, "firmwareUpdateSetDeviceFailed": "设置设备失败", "@firmwareUpdateSetDeviceFailed": {}, "firmwareUpdateErrorDuringUpdate": "升级过程出错: {error}", "@firmwareUpdateErrorDuringUpdate": {"placeholders": {"error": {"type": "String", "example": "Connection failed"}}}, "firmwareUpdateCancelledTitle": "已取消", "@firmwareUpdateCancelledTitle": {}, "firmwareUpdateCancelledMessage": "固件升级已取消", "@firmwareUpdateCancelledMessage": {}, "commonConfirm": "确认", "@commonConfirm": {}, "checkFirmwareUpdate": "检查固件更新", "@checkFirmwareUpdate": {"description": "Text for checking for firmware updates"}, "deviceRebootTitle": "设备重启", "@deviceRebootTitle": {"description": "Title for device reboot notification"}, "deviceRebootMessage": "设备正在重新启动，请稍候...", "@deviceRebootMessage": {"description": "Message content for device reboot notification"}, "infoTitle": "提示信息", "@infoTitle": {"description": "Title for information dialog"}, "errorOpeningFile": "打开文件错误: {error}", "@errorOpeningFile": {"placeholders": {"error": {"type": "String", "example": "File not found"}}}, "fileExported": "文件已导出", "@fileExported": {"description": "Message shown when a file has been exported"}, "shareFile": "分享文件", "@shareFile": {"description": "Button text for sharing a file"}, "eqWavePainterStarted": "EQ波形绘制器已启动。尺寸: {size}", "@eqWavePainterStarted": {"placeholders": {"size": {"type": "String", "example": "Size(360.0, 240.0)"}}}, "targetRawDataPrepared": "目标原始数据准备完成(已插值): {count} 个点", "@targetRawDataPrepared": {"placeholders": {"count": {"type": "int", "example": "200"}}}, "sourceFRRawDataPrepared": "源频响原始数据准备完成(已插值): {count} 个点", "@sourceFRRawDataPrepared": {"placeholders": {"count": {"type": "int", "example": "200"}}}, "combinedRawDataPrepared": "组合原始数据准备完成: {count} 个点", "@combinedRawDataPrepared": {"placeholders": {"count": {"type": "int", "example": "200"}}}, "individualBandsRawDataPrepared": "单个波段原始数据准备完成: {bandCount} 个波段, 总点数: {pointCount}", "@individualBandsRawDataPrepared": {"placeholders": {"bandCount": {"type": "int", "example": "5"}, "pointCount": {"type": "int", "example": "1000"}}}, "filteredFRRawDataPrepared": "滤波后频响原始数据准备完成(基于插值后的源): {count} 个点", "@filteredFRRawDataPrepared": {"placeholders": {"count": {"type": "int", "example": "200"}}}, "dynamicDbRangeCalculated": "动态dB范围计算完成: 最小值={min}, 最大值={max}", "@dynamicDbRangeCalculated": {"placeholders": {"min": {"type": "String", "example": "-30.0"}, "max": {"type": "String", "example": "30.0"}}}, "coordinateConverterCreated": "坐标转换器已创建，并调整了图表高度", "@coordinateConverterCreated": {"description": "Log message when coordinate converter is created"}, "gridAndLabelsDrawn": "网格和标签已绘制", "@gridAndLabelsDrawn": {"description": "Log message when grid and labels are drawn"}, "drawingTargetCurve": "正在绘制目标曲线...", "@drawingTargetCurve": {"description": "Log message when drawing target curve"}, "drawingSourceFRCurve": "正在绘制源频响曲线...", "@drawingSourceFRCurve": {"description": "Log message when drawing source FR curve"}, "drawingIndividualBands": "正在绘制单个波段...", "@drawingIndividualBands": {"description": "Log message when drawing individual bands"}, "drawingCombinedCurve": "正在绘制组合曲线...", "@drawingCombinedCurve": {"description": "Log message when drawing combined curve"}, "drawingFilteredFRCurve": "正在绘制滤波后频响曲线...", "@drawingFilteredFRCurve": {"description": "Log message when drawing filtered FR curve"}, "drawingFlatLine": "正在绘制平直线...", "@drawingFlatLine": {"description": "Log message when drawing flat line"}, "eqWavePainterFinished": "EQ波形绘制器完成", "@eqWavePainterFinished": {"description": "Log message when EQ wave painter finishes"}, "finalDynamicDbRange": "最终动态dB范围: 最小值={min}, 最大值={max}", "@finalDynamicDbRange": {"placeholders": {"min": {"type": "String", "example": "-30.0"}, "max": {"type": "String", "example": "30.0"}}}, "peqSettings": "PEQ设置", "@peqSettings": {"description": "PEQ settings page title"}, "importExport": "导入导出", "@importExport": {"description": "Import and export section header"}, "configManagement": "配置管理", "@configManagement": {"description": "Configuration management section header"}, "addConfig": "添加配置", "@addConfig": {"description": "Add configuration dialog title"}, "configName": "配置名称", "@configName": {"description": "Configuration name input label"}, "description": "描述 (可选)", "@description": {"description": "Description input label"}, "configNameHint": "例如：低音增强，清晰人声等", "@configNameHint": {"description": "Configuration name hint text"}, "configDescriptionHint": "简要描述这个配置的用途", "@configDescriptionHint": {"description": "Configuration description hint text"}, "add": "添加", "@add": {"description": "Add button text"}, "delete": "删除", "@delete": {"description": "Delete button text"}, "addNewConfig": "添加新配置", "@addNewConfig": {"description": "Add new configuration tooltip"}, "importTarget": "导入目标", "@importTarget": {"description": "Import target button text"}, "importSourceFR": "导入源频响", "@importSourceFR": {"description": "Import source FR button text"}, "exportCombinedFilter": "导出合并滤波", "@exportCombinedFilter": {"description": "Export combined filter button text"}, "targetFR": "目标频响", "@targetFR": {"description": "Target frequency response label"}, "editConfig": "编辑配置", "@editConfig": {"description": "Edit configuration dialog title/tooltip"}, "deleteConfig": "删除配置", "@deleteConfig": {"description": "Delete configuration dialog title/tooltip"}, "deleteConfigConfirmation": "确定要删除配置 \"{name}\" 吗？此操作不可撤销。", "@deleteConfigConfirmation": {"description": "Delete configuration confirmation message", "placeholders": {"name": {"type": "Object"}}}, "deleteTargetFile": "删除目标文件", "@deleteTargetFile": {"description": "Delete target file dialog title"}, "deleteSourceFRFile": "删除源频响文件", "@deleteSourceFRFile": {"description": "Delete source FR file dialog title"}, "deleteFileConfirmation": "确定要删除文件 \"{name}\" 吗？此操作不可撤销。", "@deleteFileConfirmation": {"description": "Delete file confirmation message", "placeholders": {"name": {"type": "Object"}}}, "noConfigsMessage": "还没有配置，点击\"+\"创建一个新配置", "@noConfigsMessage": {"description": "Message shown when there are no configurations"}, "devicePowerOff": "设备已关机", "@devicePowerOff": {"description": "Device is powered off"}, "devicePowerOffHint": "开启电源后方可控制设备", "@devicePowerOffHint": {"description": "Turn on the power to control the device"}, "powerOn": "开机", "@powerOn": {"description": "Power on button text"}, "inputOptical1": "光纤1", "@inputOptical1": {"description": "Optical 1 input"}, "inputOptical2": "光纤2", "@inputOptical2": {"description": "Optical 2 input"}, "inputCoaxial1": "同轴1", "@inputCoaxial1": {"description": "Coaxial 1 input"}, "inputCoaxial2": "同轴2", "@inputCoaxial2": {"description": "Coaxial 2 input"}, "inputAes": "AES", "@inputAes": {"description": "AES input"}, "inputIis": "IIS", "@inputIis": {"description": "IIS input"}, "outputDac": "DAC", "@outputDac": {"description": "DAC output"}, "outputPreamp": "前级", "@outputPreamp": {"description": "Preamp output"}, "outputAll": "全部", "@outputAll": {"description": "All outputs"}, "auto": "自动", "@auto": {"description": "Auto mode"}, "enabled": "启用", "@enabled": {"description": "Enabled state"}, "@disabled": {"description": "Disabled state"}, "standard": "标准", "@standard": {"description": "Standard mode"}, "inverted": "反相", "@inverted": {"description": "Inverted mode"}, "swapped": "交换", "@swapped": {"description": "Swapped mode"}, "displaySignal": "信号", "@displaySignal": {"description": "Signal display"}, "display12V": "12V", "@display12V": {"description": "12V display"}, "displayOff": "关闭", "@displayOff": {"description": "Display off"}, "@multiFunctionKeyPeqSelect": {"description": "PEQ select function key"}, "usbTypeC": "Type-C", "@usbTypeC": {"description": "USB Type-C"}, "usbTypeB": "Type-B", "@usbTypeB": {"description": "USB Type-B"}, "powerTriggerOff": "关闭", "@powerTriggerOff": {"description": "Power trigger off"}, "powerTrigger12V": "12V", "@powerTrigger12V": {"description": "Power trigger 12V"}, "multiFunctionKeyOutputSelect": "输出选择", "@multiFunctionKeyOutputSelect": {"description": "Output select function key"}, "multiFunctionKeyScreenOff": "息屏", "@multiFunctionKeyScreenOff": {"description": "Screen off function key"}, "usbSelect": "USB选择", "usbDsdPassthrough": "USB DSD直通", "iisPhase": "IIS相位", "iisDsdChannel": "IIS DSD通道"}