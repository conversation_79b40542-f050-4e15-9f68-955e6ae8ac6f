import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_ble_control/utils/log_util.dart';

import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';

import '../../../common/util/i18n.dart';
import '../../../enums/d900/d900_display_type.dart';
import '../../../enums/d900/d900_input_type.dart';
import '../../../enums/d900/d900_language_type.dart';
import '../../../enums/d900/d900_output_type.dart';
import '../../../enums/d900/d900_power_trigger_type.dart';
import '../../../enums/dx5/dx5_headphone_gain_type.dart';
import '../../../enums/dx5/dx5_language_type.dart';
import '../../../enums/dx5/dx5_power_trigger_type.dart';
import '../../../models/d900_device_settings.dart';
import '../../../models/dx5_device_settings.dart';
import '../device_detail_logic.dart';
import '../device_detail_state.dart';

/// 设备控制页面
class DeviceControlPage extends StatelessWidget {
  late final DeviceDetailLogic logic;
  late final DeviceDetailState state;

  dynamic get settings => state.settings.value;

  // 添加设备类型判断
  bool get isD900Device => logic.isD900Device;

  DeviceControlPage({super.key}) {
    // 确保 DeviceDetailLogic 已经注册
    if (!Get.isRegistered<DeviceDetailLogic>()) {
      Get.put(DeviceDetailLogic());
    }
    logic = Get.find<DeviceDetailLogic>();
    state = logic.state;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: ColorPalettes.instance.card,
      child: Stack(
        children: [
          SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildDeviceInfo(context),
                  const SizedBox(height: 16),
                  _buildPowerCard(context),
                  const SizedBox(height: 6),
                  _buildVolumeCard(context),
                  const SizedBox(height: 6),
                  // 根据设备类型显示不同的卡片
                  if (!isD900Device) _buildHeadphoneCard(context),
                  if (!isD900Device) const SizedBox(height: 6),
                  _buildInputCard(context),
                  const SizedBox(height: 6),
                  _buildOutputCard(context),
                  const SizedBox(height: 6),
                  _buildDisplayCard(context),
                  const SizedBox(height: 6),
                  _buildCategorySettingsCard(context),
                  const SizedBox(height: 6),
                  _buildIndependentFunctionsCard(context),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
          // 添加关机状态蒙层
          Obx(() => Visibility(
                visible: !(settings?.power ?? true),
                child: Container(
                  color: ColorPalettes.instance.overlay,
                  width: double.infinity,
                  height: double.infinity,
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.power_settings_new,
                          color: ColorPalettes.instance.firstText,
                          size: 60,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          l10n.devicePowerOff,
                          style: TextStyles.instance.h2(),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          l10n.devicePowerOffHint,
                          style: TextStyles.instance.h3(),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: () => logic.togglePower(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                ColorPalettes.instance.accent, // 使用强调色替代主题色
                            foregroundColor: ColorPalettes.instance.firstText,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                          ),
                          child: Text(l10n.powerOn),
                        ),
                      ],
                    ),
                  ),
                ),
              )),
        ],
      ),
    );
  }

  /// 构建设备信息显示区域
  Widget _buildDeviceInfo(BuildContext context) {
    return Column(
      children: [
        Image.asset(
          state.device.value?.image ?? '',
          height: 120,
          width: 240,
          fit: BoxFit.contain,
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Obx(() {
              final displayText = state.isD900Device
                  ? '${state.settings.value?.sampleRateDisplayText}'
                  : '${state.settings.value?.sampleRate}kHz';
              Log.i(
                  "DeviceControlPage: 显示采样率文本: '$displayText', 原始值: ${state.settings.value?.sampleRate}");
              return Text(
                displayText,
                style: TextStyles.instance.h3(),
              );
            }),
            const SizedBox(width: 16),
            Obx(() => Text(
                  '${state.settings.value?.selectedInput.localized(context)} - '
                  '${state.settings.value?.selectedOutput.localized(context)}',
                  style: TextStyles.instance.h3(),
                )),
          ],
        ),
      ],
    );
  }

  /// 构建电源开关卡片
  Widget _buildPowerCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.powerTrigger,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.powerTrigger,
              style: TextStyles.instance.h3(),
            ),
            trailing: Transform.scale(
              scale: 0.8,
              child: Switch(
                value: settings?.power ?? false,
                activeColor: ColorPalettes.instance.accent, // 使用强调色替代主题色
                onChanged: (value) => logic.togglePower(),
              ),
            ),
          )),
    );
  }

  /// 构建音量控制卡片
  Widget _buildVolumeCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.volume,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Row(
              children: [
                Text(
                  l10n.volume,
                  style: TextStyles.instance.h3(),
                ),
                const Spacer(),
                Obx(() => IconButton(
                      icon: Icon(
                        settings?.mute == true
                            ? Icons.volume_off
                            : Icons.volume_up,
                        color: ColorPalettes.instance.firstText,
                        size: 16,
                      ),
                      onPressed: () => logic.toggleMuteState(!settings!.mute),
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(),
                      iconSize: 16,
                    )),
                SizedBox(width: 4),
                Obx(() {
                  final volumeValue = settings?.volume ?? 0;
                  Log.i(
                      "【音量调试】UI显示音量值: $volumeValue, settings是否为null: ${settings == null}");
                  return Text(
                    logic.getFormattedVolumeText(volumeValue),
                    style: TextStyles.instance.h3(),
                  );
                }),
              ],
            ),
          ),
          Obx(() {
            // 获取当前音量并转为百分比值（用于滑块）
            int currentVolume = settings?.volume ?? 0;
            Log.i(
                "【音量调试】滑块显示 - 原始音量值: $currentVolume, settings是否为null: ${settings == null}");

            // 仅对非D900设备强制音量为负值
            if (!logic.isD900Device && currentVolume > 0) {
              int oldVolume = currentVolume;
              currentVolume = -currentVolume;
              Log.i("【音量调试】非D900设备音量值为正，已转换: 从 $oldVolume 变为 $currentVolume");
            }

            double volumePercentage = logic.getVolumePercentage(currentVolume);
            Log.i("【音量调试】滑块显示 - 处理后的音量值：$currentVolume，百分比：$volumePercentage");
            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Slider(
                value: volumePercentage,
                min: 0,
                max: 100,
                divisions: isD900Device ? 107 : 99,
                label: logic.getFormattedVolumeText(currentVolume),
                activeColor: ColorPalettes.instance.accent, // 使用强调色替代主题色
                inactiveColor: ColorPalettes.instance.accent
                    .withValues(alpha: 0.3), // 使用强调色的透明版本
                onChanged: (percentage) {
                  // 只更新UI，不发送命令
                  int newVolume = logic.volumeFromPercentage(percentage);
                  Log.i("【音量调试】滑块拖动 - 从百分比 $percentage 计算音量值: $newVolume");
                  state.currentSettings!.volume = newVolume;
                  state.settings.refresh(); // 刷新UI以获得拖动反馈
                },
                onChangeEnd: (percentage) {
                  int newVolume = logic.volumeFromPercentage(percentage);
                  Log.i("【音量调试】滑块拖动结束 - 设置音量值: $newVolume");
                  logic.setVolumeEnd(newVolume);
                },
              ),
            );
          }),
        ],
      ),
    );
  }

  /// 构建耳机输出卡片
  Widget _buildHeadphoneCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.headphoneOutput,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Obx(() => ListTile(
                dense: true,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                title: Text(
                  l10n.headphoneOutput,
                  style: TextStyles.instance.h3(),
                ),
                trailing: Transform.scale(
                  scale: 0.8,
                  child: Switch(
                    value: settings?.headphoneEnabled ?? false,
                    activeColor: ColorPalettes.instance.accent, // 使用强调色替代主题色
                    onChanged: (value) => logic.toggleHeadphone(value),
                  ),
                ),
              )),
          Obx(() => ListTile(
                dense: true,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                title: Text(
                  l10n.headphoneGain,
                  style: TextStyles.instance.h3(),
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      settings?.headphoneGainLevel ==
                              Dx5HeadphoneGainType.highGain
                          ? l10n.headphoneGainHigh
                          : l10n.headphoneGainLow,
                      style: TextStyles.instance.h3(),
                    ),
                    SizedBox(width: 4),
                    Icon(Icons.arrow_forward_ios,
                        color: ColorPalettes.instance.firstText, size: 12),
                  ],
                ),
                onTap: () {
                  Dx5HeadphoneGainType newValue =
                      settings?.headphoneGainLevel ==
                              Dx5HeadphoneGainType.highGain
                          ? Dx5HeadphoneGainType.lowGain
                          : Dx5HeadphoneGainType.highGain;
                  logic.setHeadphoneGain(newValue);
                },
              )),
        ],
      ),
    );
  }

  /// 构建输入卡片
  Widget _buildInputCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.inputSelect,
      child: Obx(() {
        String inputText = '';

        // 根据设备类型获取输入源文本
        if (isD900Device && settings is D900DeviceSettings) {
          var d900Settings = settings as D900DeviceSettings;
          inputText = d900Settings.selectedInput.localized(context);
        } else if (!isD900Device && settings is Dx5DeviceSettings) {
          var dx5Settings = settings as Dx5DeviceSettings;
          inputText = dx5Settings.selectedInput.localized(context);
        }

        return ListTile(
          dense: true,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
          title: Text(
            l10n.inputSelect,
            style: TextStyles.instance.h3(),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                inputText,
                style: TextStyles.instance.h3(),
              ),
              SizedBox(width: 4),
              Icon(Icons.arrow_forward_ios,
                  color: ColorPalettes.instance.firstText, size: 12),
            ],
          ),
          onTap: () {
            _showInputSelectionDialog(context);
          },
        );
      }),
    );
  }

  /// 构建输出卡片
  Widget _buildOutputCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.outputSelect,
      child: Obx(() {
        String outputText = '';

        // 根据设备类型获取输出源文本
        if (isD900Device && settings is D900DeviceSettings) {
          var d900Settings = settings as D900DeviceSettings;
          outputText = d900Settings.selectedOutput.localized(context);
        } else if (!isD900Device && settings is Dx5DeviceSettings) {
          var dx5Settings = settings as Dx5DeviceSettings;
          outputText = dx5Settings.selectedOutput.localized(context);
        }

        return ListTile(
          dense: true,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
          title: Text(
            l10n.outputSelect,
            style: TextStyles.instance.h3(),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                outputText,
                style: TextStyles.instance.h3(),
              ),
              SizedBox(width: 4),
              Icon(Icons.arrow_forward_ios,
                  color: ColorPalettes.instance.firstText, size: 12),
            ],
          ),
          onTap: () {
            _showOutputSelectionDialog(context);
          },
        );
      }),
    );
  }

  /// 构建显示卡片
  Widget _buildDisplayCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.displayView,
      child: Obx(() {
        String displayText = '';

        // 根据设备类型获取显示模式文本
        if (isD900Device && settings is D900DeviceSettings) {
          var d900Settings = settings as D900DeviceSettings;
          displayText = d900Settings.displayType.localized(context);
        } else if (!isD900Device && settings is Dx5DeviceSettings) {
          var dx5Settings = settings as Dx5DeviceSettings;
          displayText = dx5Settings.displayType.localized(context);
        }

        return ListTile(
          dense: true,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
          title: Text(
            l10n.displayView,
            style: TextStyles.instance.h3(),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                displayText,
                style: TextStyles.instance.h3(),
              ),
              SizedBox(width: 4),
              Icon(Icons.arrow_forward_ios,
                  color: ColorPalettes.instance.firstText, size: 12),
            ],
          ),
          onTap: () {
            _showDisplaySelectionDialog(context);
          },
        );
      }),
    );
  }

  /// 构建分类设置卡片
  Widget _buildCategorySettingsCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.setting,
      child: Column(
        children: [
          // 显示设置
          _buildCategorySettingItem(
            context,
            title: l10n.displaySettings,
            onTap: () => _navigateToDisplaySettings(),
          ),
          Divider(color: ColorPalettes.instance.divider, height: 1),
          // 输入设置
          _buildCategorySettingItem(
            context,
            title: l10n.inputSettings,
            onTap: () => _navigateToInputSettings(),
          ),
          Divider(color: ColorPalettes.instance.divider, height: 1),
          // 输出设置
          _buildCategorySettingItem(
            context,
            title: l10n.outputSettings,
            onTap: () => _navigateToOutputSettings(),
          ),
          Divider(color: ColorPalettes.instance.divider, height: 1),
          // 系统设置
          _buildCategorySettingItem(
            context,
            title: l10n.systemSettings,
            onTap: () => _navigateToSystemSettings(),
          ),
        ],
      ),
    );
  }

  /// 构建分类设置项
  Widget _buildCategorySettingItem(
    BuildContext context, {
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      title: Text(
        title,
        style: TextStyles.instance.h3(),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: ColorPalettes.instance.firstText,
        size: 12,
      ),
      onTap: onTap,
    );
  }

  /// 构建独立功能卡片
  Widget _buildIndependentFunctionsCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.functions,
      child: Column(
        children: [
          // 语言设置
          _buildIndependentFunctionItem(
            context,
            title: l10n.language,
            value: _getLanguageText(),
            onTap: () => _showLanguageSelectionDialog(context),
          ),
          Divider(color: ColorPalettes.instance.divider, height: 1),
          // 恢复出厂设置
          _buildIndependentFunctionItem(
            context,
            title: l10n.factoryReset,
            value: "",
            onTap: () => _showFactoryResetDialog(context),
            showArrow: false,
          ),
        ],
      ),
    );
  }

  /// 构建独立功能项
  Widget _buildIndependentFunctionItem(
    BuildContext context, {
    required String title,
    required String value,
    required VoidCallback onTap,
    bool showArrow = true,
  }) {
    return ListTile(
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      title: Text(
        title,
        style: TextStyles.instance.h3(),
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (value.isNotEmpty) ...[
            Text(
              value,
              style: TextStyles.instance.h3().copyWith(
                color: ColorPalettes.instance.secondText,
              ),
            ),
            const SizedBox(width: 8),
          ],
          if (showArrow)
            Icon(
              Icons.arrow_forward_ios,
              color: ColorPalettes.instance.firstText,
              size: 12,
            ),
        ],
      ),
      onTap: onTap,
    );
  }

  /// 构建设置卡片
  Widget _buildSettingCard(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return Card(
      color: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      margin: EdgeInsets.symmetric(vertical: 3),
      child: child,
    );
  }

  /// 显示输入选择对话框
  void _showInputSelectionDialog(BuildContext context) {
    if (isD900Device) {
      _showD900InputSelectionDialog(context);
      return;
    }
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.inputSelect,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: state.availableInputs.length,
                  itemBuilder: (context, index) {
                    final input = state.availableInputs[index];
                    bool isSelected = settings?.selectedInput == input;
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            input.localized(context),
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color:
                                  ColorPalettes.instance.accent, // 使用强调色替代主题色
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setInput(input);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示 D900 输入选择对话框
  void _showD900InputSelectionDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.inputSelect,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: state.availableInputs.length,
                  itemBuilder: (context, index) {
                    final input = state.availableInputs[index] as D900InputType;
                    bool isSelected = false;

                    if (settings is D900DeviceSettings) {
                      var d900Settings = settings as D900DeviceSettings;
                      isSelected = d900Settings.selectedInput == input;
                    }

                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            input.localized(context),
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color:
                                  ColorPalettes.instance.accent, // 使用强调色替代主题色
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setInput(input);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示输出选择对话框
  void _showOutputSelectionDialog(BuildContext context) {
    if (isD900Device) {
      _showD900OutputSelectionDialog(context);
      return;
    }
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.outputSelect,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: state.availableOutputs.length,
                  itemBuilder: (context, index) {
                    final output = state.availableOutputs[index];
                    bool isSelected = settings?.selectedOutput == output;
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            output.localized(context),
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color:
                                  ColorPalettes.instance.accent, // 使用强调色替代主题色
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setOutput(output);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示 D900 输出选择对话框
  void _showD900OutputSelectionDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.outputSelect,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: state.availableOutputs.length,
                  itemBuilder: (context, index) {
                    final output =
                        state.availableOutputs[index] as D900OutputType;
                    bool isSelected = false;

                    if (settings is D900DeviceSettings) {
                      var d900Settings = settings as D900DeviceSettings;
                      isSelected = d900Settings.selectedOutput == output;
                    }

                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            output.localized(context),
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color:
                                  ColorPalettes.instance.accent, // 使用强调色替代主题色
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setOutput(output);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示显示模式选择对话框
  void _showDisplaySelectionDialog(BuildContext context) {
    if (isD900Device) {
      _showD900DisplaySelectionDialog(context);
      return;
    }
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.displayView,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: state.availableDisplayModes.length,
                  itemBuilder: (context, index) {
                    final displayMode = state.availableDisplayModes[index];
                    bool isSelected = settings?.displayType == displayMode;
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            displayMode.localized(context),
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color:
                                  ColorPalettes.instance.accent, // 使用强调色替代主题色
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setDisplayMode(displayMode);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示 D900 显示模式选择对话框
  void _showD900DisplaySelectionDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.displayView,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: state.availableDisplayModes.length,
                  itemBuilder: (context, index) {
                    final displayMode =
                        state.availableDisplayModes[index] as D900DisplayType;
                    bool isSelected = false;

                    if (settings is D900DeviceSettings) {
                      var d900Settings = settings as D900DeviceSettings;
                      isSelected = d900Settings.displayType == displayMode;
                    }

                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            displayMode.localized(context),
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color:
                                  ColorPalettes.instance.accent, // 使用强调色替代主题色
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setDisplayMode(displayMode);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 获取语言文本
  String _getLanguageText() {
    if (isD900Device && settings is D900DeviceSettings) {
      var d900Settings = settings as D900DeviceSettings;
      return (d900Settings.language as dynamic).localized(Get.context!);
    } else {
      var dx5Settings = settings as Dx5DeviceSettings;
      return (dx5Settings.language as dynamic).localized(Get.context!);
    }
  }

  /// 获取开关机触发文本
  String _getPowerTriggerText() {
    if (isD900Device && settings is D900DeviceSettings) {
      var d900Settings = settings as D900DeviceSettings;
      return (d900Settings.powerTrigger as dynamic).localized(Get.context!);
    } else {
      var dx5Settings = settings as Dx5DeviceSettings;
      return (dx5Settings.powerTrigger as dynamic).localized(Get.context!);
    }
  }

  /// 显示语言选择对话框
  void _showLanguageSelectionDialog(BuildContext context) {
    final languages = isD900Device
        ? D900LanguageType.values
        : Dx5LanguageType.values;

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (context) {
        return Container(
          padding: EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                l10n.language,
                style: TextStyles.instance.h2(),
              ),
              Divider(color: ColorPalettes.instance.divider),
              ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: languages.length,
                itemBuilder: (context, index) {
                  final language = languages[index];
                  bool isSelected = (isD900Device && settings is D900DeviceSettings)
                      ? (settings as D900DeviceSettings).language == language
                      : (settings as Dx5DeviceSettings).language == language;

                  return ListTile(
                    title: Row(
                      children: [
                        Text(
                          (language as dynamic).localized(context),
                          style: TextStyles.instance.h3(),
                        ),
                        if (isSelected) ...[
                          SizedBox(width: 8),
                          Icon(
                            Icons.check_circle,
                            color: ColorPalettes.instance.accent,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                    onTap: () {
                      logic.setLanguage(language);
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// 显示开关机触发选择对话框
  void _showPowerTriggerSelectionDialog(BuildContext context) {
    final triggers = isD900Device
        ? D900PowerTriggerType.values
        : Dx5PowerTriggerType.values;

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (context) {
        return Container(
          padding: EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                l10n.powerTrigger,
                style: TextStyles.instance.h2(),
              ),
              Divider(color: ColorPalettes.instance.divider),
              ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: triggers.length,
                itemBuilder: (context, index) {
                  final trigger = triggers[index];
                  bool isSelected = (isD900Device && settings is D900DeviceSettings)
                      ? (settings as D900DeviceSettings).powerTrigger == trigger
                      : (settings as Dx5DeviceSettings).powerTrigger == trigger;

                  return ListTile(
                    title: Row(
                      children: [
                        Text(
                          (trigger as dynamic).localized(context),
                          style: TextStyles.instance.h3(),
                        ),
                        if (isSelected) ...[
                          SizedBox(width: 8),
                          Icon(
                            Icons.check_circle,
                            color: ColorPalettes.instance.accent,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                    onTap: () {
                      logic.setPowerTrigger(trigger);
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// 导航到显示设置页面
  void _navigateToDisplaySettings() {
    Get.toNamed(
      '/device_display_settings',
      arguments: {
        'deviceId': state.deviceId,
        'isD900Device': isD900Device,
      },
    );
  }

  /// 导航到输入设置页面
  void _navigateToInputSettings() {
    Get.toNamed(
      '/device_input_settings',
      arguments: {
        'deviceId': state.deviceId,
        'isD900Device': isD900Device,
      },
    );
  }

  /// 导航到输出设置页面
  void _navigateToOutputSettings() {
    Get.toNamed(
      '/device_output_settings',
      arguments: {
        'deviceId': state.deviceId,
        'isD900Device': isD900Device,
      },
    );
  }

  /// 导航到系统设置页面
  void _navigateToSystemSettings() {
    Get.toNamed(
      '/device_system_settings',
      arguments: {
        'deviceId': state.deviceId,
        'isD900Device': isD900Device,
      },
    );
  }

  /// 显示恢复出厂设置对话框
  void _showFactoryResetDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: ColorPalettes.instance.card,
          title: Text(
            l10n.factoryReset,
            style: TextStyles.instance.h2(),
          ),
          content: Text(
            l10n.factoryResetConfirm,
            style: TextStyles.instance.h3(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                l10n.cancel,
                style: TextStyles.instance.h3().copyWith(
                  color: ColorPalettes.instance.secondText,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                logic.factoryReset();
              },
              child: Text(
                l10n.confirm,
                style: TextStyles.instance.h3().copyWith(
                  color: ColorPalettes.instance.accent,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
