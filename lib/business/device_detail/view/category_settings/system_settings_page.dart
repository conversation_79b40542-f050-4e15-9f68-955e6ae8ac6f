import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../../../common/util/i18n.dart';
import '../../../../common/widget/background_wrapper.dart';
import '../../../../models/d900_device_settings.dart';
import '../../../../models/dx5_device_settings.dart';
import '../../device_detail_logic.dart';
import '../../device_detail_state.dart';

/// 系统设置页面
class SystemSettingsPage extends StatelessWidget {
  late final DeviceDetailLogic logic;
  late final DeviceDetailState state;
  late final String deviceId;
  late final bool isD900Device;

  dynamic get settings => state.settings.value;

  SystemSettingsPage({super.key}) {
    final args = Get.arguments as Map<String, dynamic>;
    deviceId = args['deviceId'] ?? '';
    isD900Device = args['isD900Device'] ?? false;

    // 尝试获取已存在的 DeviceDetailLogic 实例
    try {
      logic = Get.find<DeviceDetailLogic>();
      state = logic.state;
    } catch (e) {
      // 如果没有找到，创建一个新的实例
      logic = Get.put(DeviceDetailLogic());
      state = logic.state;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundWrapper(
      child: Scaffold(
        backgroundColor: ColorPalettes.instance.transparent,
        appBar: AppBar(
          backgroundColor: ColorPalettes.instance.card,
          title: Text(
            l10n.systemSettings,
            style: TextStyles.instance.h2(),
          ),
          centerTitle: true,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: ColorPalettes.instance.firstText,
            ),
            onPressed: () => Get.back(),
          ),
          iconTheme: IconThemeData(color: ColorPalettes.instance.firstText),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildPowerTriggerCard(context),
                const SizedBox(height: 6),
                if (isD900Device) ...[
                  _buildDsdDirectCard(context),
                  const SizedBox(height: 6),
                ],
                _buildVolumeMemoryCard(context),
                const SizedBox(height: 6),
                _buildPeqMemoryCard(context),
                const SizedBox(height: 6),
                _buildRemoteControlCard(context),
                const SizedBox(height: 6),
                _buildMainButtonFunctionCard(context),
                const SizedBox(height: 6),
                _buildRemoteAButtonFunctionCard(context),
                const SizedBox(height: 6),
                _buildRemoteBButtonFunctionCard(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建开关机触发卡片
  Widget _buildPowerTriggerCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.powerTrigger,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.powerTrigger,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getPowerTriggerText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showPowerTriggerSelectionDialog(context),
          )),
    );
  }

  /// 构建DSD直通卡片 (D900专用)
  Widget _buildDsdDirectCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "DSD直通",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "DSD直通",
              style: TextStyles.instance.h3(),
            ),
            trailing: Transform.scale(
              scale: 0.8,
              child: Switch(
                value: _getDsdDirectEnabled(),
                activeColor: ColorPalettes.instance.accent,
                onChanged: (value) => _setDsdDirectEnabled(value),
              ),
            ),
          )),
    );
  }

  /// 构建音量记忆方式卡片
  Widget _buildVolumeMemoryCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "音量记忆方式",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "音量记忆方式",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getVolumeMemoryText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showVolumeMemorySelectionDialog(context),
          )),
    );
  }

  /// 构建PEQ记忆方式卡片
  Widget _buildPeqMemoryCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "PEQ记忆方式",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "PEQ记忆方式",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getPeqMemoryText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showPeqMemorySelectionDialog(context),
          )),
    );
  }

  /// 构建遥控器卡片
  Widget _buildRemoteControlCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.relay,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.relay,
              style: TextStyles.instance.h3(),
            ),
            trailing: Transform.scale(
              scale: 0.8,
              child: Switch(
                value: _getRemoteControlEnabled(),
                activeColor: ColorPalettes.instance.accent,
                onChanged: (value) => _setRemoteControlEnabled(value),
              ),
            ),
          )),
    );
  }

  /// 构建主按键功能卡片
  Widget _buildMainButtonFunctionCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "主按键功能",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "主按键功能",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getMainButtonFunctionText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showMainButtonFunctionSelectionDialog(context),
          )),
    );
  }

  /// 构建遥控A键功能卡片
  Widget _buildRemoteAButtonFunctionCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "遥控A键功能",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "遥控A键功能",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getRemoteAButtonFunctionText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showRemoteAButtonFunctionSelectionDialog(context),
          )),
    );
  }

  /// 构建遥控B键功能卡片
  Widget _buildRemoteBButtonFunctionCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "遥控B键功能",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "遥控B键功能",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getRemoteBButtonFunctionText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showRemoteBButtonFunctionSelectionDialog(context),
          )),
    );
  }

  /// 构建设置卡片
  Widget _buildSettingCard(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return Card(
      color: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      margin: EdgeInsets.symmetric(vertical: 3),
      child: child,
    );
  }

  /// 获取开关机触发文本
  String _getPowerTriggerText() {
    int triggerValue = _getPowerTriggerValue();
    switch (triggerValue) {
      case 0:
        return "信号";
      case 1:
        return "12V";
      case 2:
        return "关闭";
      default:
        return "信号";
    }
  }

  /// 获取开关机触发值
  int _getPowerTriggerValue() {
    if (settings != null) {
      if (isD900Device && settings is D900DeviceSettings) {
        var d900Settings = settings as D900DeviceSettings;
        return d900Settings.powerTrigger.index;
      } else if (!isD900Device && settings is Dx5DeviceSettings) {
        var dx5Settings = settings as Dx5DeviceSettings;
        return dx5Settings.powerTrigger.index;
      }
    }
    return 0;
  }

  /// 获取DSD直通状态 (D900)
  bool _getDsdDirectEnabled() {
    // TODO: 实现DSD直通状态获取
    return true;
  }

  /// 设置DSD直通状态 (D900)
  void _setDsdDirectEnabled(bool value) {
    // TODO: 实现DSD直通状态设置
    Get.snackbar("提示", "DSD直通设置开发中");
  }

  /// 获取音量记忆方式文本
  String _getVolumeMemoryText() {
    // TODO: 实现音量记忆方式文本获取
    return "跟随输出";
  }

  /// 获取PEQ记忆方式文本
  String _getPeqMemoryText() {
    // TODO: 实现PEQ记忆方式文本获取
    return "跟随输出";
  }

  /// 获取遥控器状态
  bool _getRemoteControlEnabled() {
    // TODO: 实现遥控器状态获取
    return true;
  }

  /// 设置遥控器状态
  void _setRemoteControlEnabled(bool value) {
    // TODO: 实现遥控器状态设置
    Get.snackbar("提示", "遥控器设置开发中");
  }

  /// 获取主按键功能文本
  String _getMainButtonFunctionText() {
    // TODO: 实现主按键功能文本获取
    return isD900Device ? "输出选择" : "输入选择";
  }

  /// 获取遥控A键功能文本
  String _getRemoteAButtonFunctionText() {
    // TODO: 实现遥控A键功能文本获取
    return "输入选择";
  }

  /// 获取遥控B键功能文本
  String _getRemoteBButtonFunctionText() {
    // TODO: 实现遥控B键功能文本获取
    return "输出选择";
  }

  /// 显示开关机触发选择对话框
  void _showPowerTriggerSelectionDialog(BuildContext context) {
    final triggerList = [
      {'name': '信号', 'value': 0},
      {'name': '12V', 'value': 1},
      {'name': '关闭', 'value': 2},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.powerTrigger,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: triggerList.length,
                  itemBuilder: (context, index) {
                    final trigger = triggerList[index];
                    bool isSelected = _getPowerTriggerValue() == trigger['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            trigger['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setPowerTrigger(trigger['value']);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示音量记忆方式选择对话框
  void _showVolumeMemorySelectionDialog(BuildContext context) {
    // TODO: 实现音量记忆方式选择对话框
    Get.snackbar("提示", "音量记忆方式选择功能开发中");
  }

  /// 显示PEQ记忆方式选择对话框
  void _showPeqMemorySelectionDialog(BuildContext context) {
    // TODO: 实现PEQ记忆方式选择对话框
    Get.snackbar("提示", "PEQ记忆方式选择功能开发中");
  }

  /// 显示主按键功能选择对话框
  void _showMainButtonFunctionSelectionDialog(BuildContext context) {
    // TODO: 实现主按键功能选择对话框
    Get.snackbar("提示", "主按键功能选择功能开发中");
  }

  /// 显示遥控A键功能选择对话框
  void _showRemoteAButtonFunctionSelectionDialog(BuildContext context) {
    // TODO: 实现遥控A键功能选择对话框
    Get.snackbar("提示", "遥控A键功能选择功能开发中");
  }

  /// 显示遥控B键功能选择对话框
  void _showRemoteBButtonFunctionSelectionDialog(BuildContext context) {
    // TODO: 实现遥控B键功能选择对话框
    Get.snackbar("提示", "遥控B键功能选择功能开发中");
  }
}
