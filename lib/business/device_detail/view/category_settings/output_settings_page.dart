import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../../../common/util/i18n.dart';
import '../../../../common/widget/background_wrapper.dart';
import '../../../../models/d900_device_settings.dart';
import '../../../../models/dx5_device_settings.dart';
import '../../device_detail_logic.dart';
import '../../device_detail_state.dart';

/// 输出设置页面
class OutputSettingsPage extends StatelessWidget {
  late final DeviceDetailLogic logic;
  late final DeviceDetailState state;
  late final String deviceId;
  late final bool isD900Device;

  dynamic get settings => state.settings.value;

  OutputSettingsPage({super.key}) {
    final args = Get.arguments as Map<String, dynamic>;
    deviceId = args['deviceId'] ?? '';
    isD900Device = args['isD900Device'] ?? false;
    
    // 确保 DeviceDetailLogic 已经注册
    if (!Get.isRegistered<DeviceDetailLogic>()) {
      Get.put(DeviceDetailLogic());
    }
    logic = Get.find<DeviceDetailLogic>();
    state = logic.state;
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundWrapper(
      child: Scaffold(
        backgroundColor: ColorPalettes.instance.transparent,
        appBar: AppBar(
          backgroundColor: ColorPalettes.instance.card,
          title: Text(
            l10n.outputSettings,
            style: TextStyles.instance.h2(),
          ),
          centerTitle: true,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: ColorPalettes.instance.firstText,
            ),
            onPressed: () => Get.back(),
          ),
          iconTheme: IconThemeData(color: ColorPalettes.instance.firstText),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildOutputSelectCard(context),
                const SizedBox(height: 6),
                _buildChannelBalanceCard(context),
                const SizedBox(height: 6),
                if (!isD900Device) ...[
                  _buildPcmFilterCard(context),
                  const SizedBox(height: 6),
                  _buildHeadphoneGainCard(context),
                  const SizedBox(height: 6),
                ],
                _buildVolumeStepCard(context),
                const SizedBox(height: 6),
                _buildPolarityCard(context),
                const SizedBox(height: 6),
                if (isD900Device) ...[
                  _buildOutputLevelCard(context),
                ] else ...[
                  _buildLineModeCard(context),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建输出选择卡片
  Widget _buildOutputSelectCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.outputSelect,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.outputSelect,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getOutputText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showOutputSelectionDialog(context),
          )),
    );
  }

  /// 构建声道平衡度卡片
  Widget _buildChannelBalanceCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.audioBalance,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.audioBalance,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getChannelBalanceText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showChannelBalanceDialog(context),
          )),
    );
  }

  /// 构建PCM滤波器卡片 (DX5专用)
  Widget _buildPcmFilterCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "PCM滤波器",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "PCM滤波器",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getPcmFilterText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showPcmFilterSelectionDialog(context),
          )),
    );
  }

  /// 构建耳放增益卡片 (DX5专用)
  Widget _buildHeadphoneGainCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.headphoneGain,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.headphoneGain,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getHeadphoneGainText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showHeadphoneGainSelectionDialog(context),
          )),
    );
  }

  /// 构建音量步进设置卡片
  Widget _buildVolumeStepCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "音量步进设置",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "音量步进设置",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getVolumeStepText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showVolumeStepSelectionDialog(context),
          )),
    );
  }

  /// 构建极性设置卡片
  Widget _buildPolarityCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "极性设置",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "极性设置",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getPolarityText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showPolaritySelectionDialog(context),
          )),
    );
  }

  /// 构建输出幅值卡片 (D900专用)
  Widget _buildOutputLevelCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "输出幅值",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "输出幅值",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getOutputLevelText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showOutputLevelSelectionDialog(context),
          )),
    );
  }

  /// 构建线路模式卡片 (DX5专用)
  Widget _buildLineModeCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "线路模式",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "线路模式",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getLineModeText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showLineModeSelectionDialog(context),
          )),
    );
  }

  /// 构建设置卡片
  Widget _buildSettingCard(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return Card(
      color: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      margin: EdgeInsets.symmetric(vertical: 3),
      child: child,
    );
  }

  /// 获取输出源文本
  String _getOutputText() {
    if (settings != null) {
      return settings.selectedOutput?.localized(Get.context!) ?? "全部";
    }
    return "全部";
  }

  /// 获取声道平衡度文本
  String _getChannelBalanceText() {
    // TODO: 实现声道平衡度文本获取
    return "C";
  }

  /// 获取PCM滤波器文本 (DX5)
  String _getPcmFilterText() {
    // TODO: 实现PCM滤波器文本获取
    return "F-1";
  }

  /// 获取耳放增益文本 (DX5)
  String _getHeadphoneGainText() {
    // TODO: 实现耳放增益文本获取
    return l10n.low;
  }

  /// 获取音量步进设置文本
  String _getVolumeStepText() {
    // TODO: 实现音量步进设置文本获取
    return "0.5dB";
  }

  /// 获取极性设置文本
  String _getPolarityText() {
    // TODO: 实现极性设置文本获取
    return "标准";
  }

  /// 获取输出幅值文本 (D900)
  String _getOutputLevelText() {
    // TODO: 实现输出幅值文本获取
    return "5V";
  }

  /// 获取线路模式文本 (DX5)
  String _getLineModeText() {
    // TODO: 实现线路模式文本获取
    return "前级";
  }

  /// 显示输出选择对话框
  void _showOutputSelectionDialog(BuildContext context) {
    // TODO: 实现输出选择对话框
    Get.snackbar("提示", "输出选择功能开发中");
  }

  /// 显示声道平衡度对话框
  void _showChannelBalanceDialog(BuildContext context) {
    // TODO: 实现声道平衡度对话框
    Get.snackbar("提示", "声道平衡度设置功能开发中");
  }

  /// 显示PCM滤波器选择对话框
  void _showPcmFilterSelectionDialog(BuildContext context) {
    // TODO: 实现PCM滤波器选择对话框
    Get.snackbar("提示", "PCM滤波器选择功能开发中");
  }

  /// 显示耳放增益选择对话框
  void _showHeadphoneGainSelectionDialog(BuildContext context) {
    // TODO: 实现耳放增益选择对话框
    Get.snackbar("提示", "耳放增益选择功能开发中");
  }

  /// 显示音量步进设置选择对话框
  void _showVolumeStepSelectionDialog(BuildContext context) {
    // TODO: 实现音量步进设置选择对话框
    Get.snackbar("提示", "音量步进设置选择功能开发中");
  }

  /// 显示极性设置选择对话框
  void _showPolaritySelectionDialog(BuildContext context) {
    // TODO: 实现极性设置选择对话框
    Get.snackbar("提示", "极性设置选择功能开发中");
  }

  /// 显示输出幅值选择对话框
  void _showOutputLevelSelectionDialog(BuildContext context) {
    // TODO: 实现输出幅值选择对话框
    Get.snackbar("提示", "输出幅值选择功能开发中");
  }

  /// 显示线路模式选择对话框
  void _showLineModeSelectionDialog(BuildContext context) {
    // TODO: 实现线路模式选择对话框
    Get.snackbar("提示", "线路模式选择功能开发中");
  }
}
