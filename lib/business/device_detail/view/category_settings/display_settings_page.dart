import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../../../common/util/i18n.dart';
import '../../../../common/widget/background_wrapper.dart';
import '../../../../models/d900_device_settings.dart';
import '../../device_detail_logic.dart';
import '../../device_detail_state.dart';

/// 显示设置页面
class DisplaySettingsPage extends StatelessWidget {
  late final DeviceDetailLogic logic;
  late final DeviceDetailState state;
  late final String deviceId;
  late final bool isD900Device;

  dynamic get settings => state.settings.value;

  DisplaySettingsPage({super.key}) {
    final args = Get.arguments as Map<String, dynamic>;
    deviceId = args['deviceId'] ?? '';
    isD900Device = args['isD900Device'] ?? false;

    // 尝试获取已存在的 DeviceDetailLogic 实例
    try {
      logic = Get.find<DeviceDetailLogic>();
      state = logic.state;
    } catch (e) {
      // 如果没有找到，创建一个新的实例
      logic = Get.put(DeviceDetailLogic());
      state = logic.state;
      // 设置设备ID
      if (deviceId.isNotEmpty) {
        state.deviceId = deviceId;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundWrapper(
      child: Scaffold(
        backgroundColor: ColorPalettes.instance.transparent,
        appBar: AppBar(
          backgroundColor: ColorPalettes.instance.card,
          title: Text(
            l10n.displaySettings,
            style: TextStyles.instance.h2(),
          ),
          centerTitle: true,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: ColorPalettes.instance.firstText,
            ),
            onPressed: () => Get.back(),
          ),
          iconTheme: IconThemeData(color: ColorPalettes.instance.firstText),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildDisplayViewCard(context),
                const SizedBox(height: 6),
                _buildThemeCard(context),
                const SizedBox(height: 6),
                _buildHomePageCard(context),
                const SizedBox(height: 6),
                _buildBrightnessCard(context),
                const SizedBox(height: 6),
                _buildVuMeterLevelCard(context),
                const SizedBox(height: 6),
                _buildVuMeterDisplayCard(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建显示界面卡片
  Widget _buildDisplayViewCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.displayView,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.displayView,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getDisplayModeText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showDisplayModeSelectionDialog(context),
          )),
    );
  }

  /// 构建主题设置卡片
  Widget _buildThemeCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.theme,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.theme,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getThemeText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showThemeSelectionDialog(context),
          )),
    );
  }

  /// 构建主页设置卡片
  Widget _buildHomePageCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "主页",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "主页",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getHomePageText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showHomePageSelectionDialog(context),
          )),
    );
  }

  /// 构建亮度设置卡片
  Widget _buildBrightnessCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.screenBrightness,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.screenBrightness,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getBrightnessText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showBrightnessSelectionDialog(context),
          )),
    );
  }

  /// 构建VU表0dDB幅值设置卡片
  Widget _buildVuMeterLevelCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "经典VU 0dDB幅值",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "经典VU 0dDB幅值",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getVuMeterLevelText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showVuMeterLevelSelectionDialog(context),
          )),
    );
  }

  /// 构建VU条显示模式设置卡片
  Widget _buildVuMeterDisplayCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "VU条显示模式",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "VU条显示模式",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getVuMeterDisplayText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showVuMeterDisplaySelectionDialog(context),
          )),
    );
  }

  /// 构建设置卡片
  Widget _buildSettingCard(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return Card(
      color: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      margin: EdgeInsets.symmetric(vertical: 3),
      child: child,
    );
  }

  /// 获取主题文本
  String _getThemeText() {
    // TODO: 实现主题文本获取
    return "极光";
  }

  /// 获取显示模式文本
  String _getDisplayModeText() {
    if (settings != null) {
      return settings.displayType?.localized(Get.context!) ?? "常规";
    }
    return "常规";
  }

  /// 获取主页文本
  String _getHomePageText() {
    // TODO: 实现主页文本获取
    return "常规";
  }

  /// 获取亮度文本
  String _getBrightnessText() {
    // TODO: 实现亮度文本获取
    return l10n.middle;
  }

  /// 获取VU表0dDB幅值文本
  String _getVuMeterLevelText() {
    // TODO: 实现VU表0dDB幅值文本获取
    return "+4dBu";
  }

  /// 获取VU条显示模式文本
  String _getVuMeterDisplayText() {
    // TODO: 实现VU条显示模式文本获取
    return "全开";
  }

  /// 显示主题选择对话框
  void _showThemeSelectionDialog(BuildContext context) {
    // TODO: 实现主题选择对话框
    Get.snackbar("提示", "主题选择功能开发中");
  }

  /// 显示显示模式选择对话框
  void _showDisplayModeSelectionDialog(BuildContext context) {
    if (isD900Device) {
      _showD900DisplaySelectionDialog(context);
      return;
    }
    _showDx5DisplaySelectionDialog(context);
  }

  /// 显示DX5显示模式选择对话框
  void _showDx5DisplaySelectionDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.displayView,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: state.availableDisplayModes.length,
                  itemBuilder: (context, index) {
                    final displayMode = state.availableDisplayModes[index];
                    bool isSelected = settings?.displayType == displayMode;
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            displayMode.localized(context),
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setDisplayMode(displayMode);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示D900显示模式选择对话框
  void _showD900DisplaySelectionDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.displayView,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: state.availableDisplayModes.length,
                  itemBuilder: (context, index) {
                    final displayMode = state.availableDisplayModes[index];
                    bool isSelected = false;

                    if (settings is D900DeviceSettings) {
                      var d900Settings = settings as D900DeviceSettings;
                      isSelected = d900Settings.displayType == displayMode;
                    }

                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            displayMode.localized(context),
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setDisplayMode(displayMode);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示主页选择对话框
  void _showHomePageSelectionDialog(BuildContext context) {
    // TODO: 实现主页选择对话框
    Get.snackbar("提示", "主页选择功能开发中");
  }

  /// 显示亮度选择对话框
  void _showBrightnessSelectionDialog(BuildContext context) {
    // TODO: 实现亮度选择对话框
    Get.snackbar("提示", "亮度选择功能开发中");
  }

  /// 显示VU表0dDB幅值选择对话框
  void _showVuMeterLevelSelectionDialog(BuildContext context) {
    // TODO: 实现VU表0dDB幅值选择对话框
    Get.snackbar("提示", "VU表0dDB幅值选择功能开发中");
  }

  /// 显示VU条显示模式选择对话框
  void _showVuMeterDisplaySelectionDialog(BuildContext context) {
    // TODO: 实现VU条显示模式选择对话框
    Get.snackbar("提示", "VU条显示模式选择功能开发中");
  }
}
